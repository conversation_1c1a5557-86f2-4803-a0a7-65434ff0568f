import os
import json
from bs4 import BeautifulSoup

def extract_table_with_span(table):
    rows = table.find_all("tr")
    table_data = []

    for row in rows:
        row_data = []
        for cell in row.find_all(["th", "td"]):
            row_data.append({
                "tag": cell.name,
                "text": cell.get_text(strip=True),
                "rowspan": int(cell.get("rowspan", 1)),
                "colspan": int(cell.get("colspan", 1))
            })
        table_data.append(row_data)
    return table_data

def extract_table_no_span(table):
    rows = table.find_all("tr")
    simplified = []

    for row in rows:
        row_data = []
        for cell in row.find_all(["th", "td"]):
            row_data.append(cell.get_text(strip=True))
        simplified.append(row_data)
    return simplified

def process_tables_from_html(html):
    soup = BeautifulSoup(html, "html.parser")
    tables = soup.find_all("table")
    output = []

    for idx, table in enumerate(tables, 1):
        caption = table.caption.get_text(strip=True) if table.caption else None
        table_entry = {
            "index": idx,
            "caption": caption,
            "with_span": extract_table_with_span(table),
            "no_span": extract_table_no_span(table)
        }
        output.append(table_entry)
    return output

def main():
    input_file = "di_parser_output.txt"  # your input HTML file
    output_dir = "output_test"
    os.makedirs(output_dir, exist_ok=True)

    with open(input_file, "r", encoding="utf-8") as f:
        html = f.read()

    tables = process_tables_from_html(html)

    for table in tables:
        filename = f"table_{table['index']}.json"
        filepath = os.path.join(output_dir, filename)
        with open(filepath, "w", encoding="utf-8") as out_file:
            json.dump(table, out_file, indent=2, ensure_ascii=False)

    print(f"✅ Extracted and saved {len(tables)} tables to '{output_dir}/'.")

if __name__ == "__main__":
    main()
