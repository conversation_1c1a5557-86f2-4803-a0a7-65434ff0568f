=== COMPREHENSIVE TABLE CONVERSION OUTPUTS ===
Auto-discovered files processed in order:
1. di_parser_output.txt (if exists)
2. All .txt files in test/ folder (sorted naturally)

=== DI_PARSER_OUTPUT.TXT ===
{'content': '# Design and build accessible PDF tables Sample tables<table><caption>Table 1</caption>[{"Row":"Row header (TH)","Column header (TH)":"Data cell (TD)"},{"Row":"Row header (TH)","Column header (TH)":"Data cell (TD)"},{"Row":"Row header(TH)","Column header (TH)":"Data cell (TD)"},{"Row":"Row header(TH)","Column header (TH)":"Data cell (TD)"}]</table><table><caption>Table 2: example of footnotes referenced from within a table</caption>{"Policy functions":{"Financial":{"2009/10":"22.5","2010/11 1":"30.57"},"Information 2":{"2009/10":"10.2","2010/11 1":"14.8"},"Contingency":{"2009/10":"2.6","2010/11 1":"1.2"}},"Remunerated functions":{"Agency services 3":{"2009/10":"44.7","2010/11 1":"35.91"},"Payments":{"2009/10":"22.41","2010/11 1":"19.88"},"Banking":{"2009/10":"22.90","2010/11 1":"44.23"},"Other":{"2009/10":"12.69","2010/11 1":"10.32"}}}</table>(1) Provisional total as of publication date.(2) Costs associated with on-going information programmes.(3) From the management accounts, net of recoveries, including interest charges.<table><caption>Table 3: "film credits" style layout</caption>{"Main character":"Daniel Radcliffe","Sidekick 1":"Rupert Grint","Sidekick 2":"Emma Watson","Lovable ogre":"Robbie Coltrane","Professor":"Maggie Smith","Headmaster":"Richard Harris"}</table><!-- PageBreak -->**Page 1**<table><caption>Table 4: table 3 with column headers added</caption>[{"Role":"Main character","Actor":"Daniel Radcliffe"},{"Role":"Sidekick 1","Actor":"Rupert Grint"},{"Role":"Sidekick 2","Actor":"Emma Watson"},{"Role":"Lovable ogre","Actor":"Robbie Coltrane"},{"Role":"Professor","Actor":"Maggie Smith"},{"Role":"Headmaster","Actor":"Richard Harris"}]</table><table><caption>Table 5: year-end financial statement (£, thousands)</caption>{"Non-current assets":{"Property":{"2010":"345","2009":"445","2008":"222"},"Investment":{"2010":"567","2009":"654","2008":"423"},"Intangibles":{"2010":"423","2009":"123","2008":"453"}},"Current assets":{"Trade and other receivables":{"2010":"435","2009":"634","2008":"231"},"Cash and cash equivalents":{"2010":"524","2009":"123","2008":"482"},"Other":{"2010":"223","2009":"211","2008":"254"}}}</table><table><caption>Table 6: a table with a more serious headings problem</caption><title>Rainfall (inches)</title>{"2010":{"Average":{"Americas":"104","Asia":"201","Europe":"193","Africa":"144"},"24 hour high":{"Americas":"15","Asia":"26","Europe":"27","Africa":"18"},"12 hour high":{"Americas":"9","Asia":"10","Europe":"11","Africa":"12"}},"2009":{"Average":{"Americas":"133","Asia":"244","Europe":"155","Africa":"166"},"24 hour high":{"Americas":"27","Asia":"28","Europe":"29","Africa":"20"},"12 hour high":{"Americas":"11","Asia":"12","Europe":"13","Africa":"16"}}}</table><!-- PageBreak -->**Page 2**<table><caption>Table 7: year-end statement, non-current assets (£, thousands)</caption>[{"Non-current assets":"Property","2010":"345","2009":"445","2008":"222"},{"Non-current assets":"Investment","2010":"567","2009":"654","2008":"423"},{"Non-current assets":"Intangibles","2010":"423","2009":"123","2008":"453"}]</table><table><caption>Table 8: year-end statement, current assets (£, thousands)</caption>[{"Current assets":"Trade and other receivables","2010":"435","2009":"634","2008":"231"},{"Current assets":"Cash and cash equivalents","2010":"524","2009":"123","2008":"482"},{"Current assets":"Other","2010":"223","2009":"211","2008":"254"}]</table><table><caption>Table 9: rainfall by continent, 2009</caption>[{"Rainfall (inches)":"Average","Americas":"133","Asia":"244","Europe":"155","Africa":"166"},{"Rainfall (inches)":"24 hour high","Americas":"27","Asia":"28","Europe":"29","Africa":"20"},{"Rainfall (inches)":"12 hour high","Americas":"11","Asia":"12","Europe":"13","Africa":"16"}]</table><!-- PageBreak -->**Page 3**<table><caption>Table 10: self-contained year-end statement (£, thousands) (multiple layout problems)</caption>{"General income":{"2011":"250,000","2010 restated":"200,000"},"Increase in value, WIP":{"2011":"15,000","2010 restated":"30,000"},"Administrative costs":{"Staff costs":{"2011":"(200,000)","2010 restated":"(150,000)"},"Early departures":{"2011":"(10,000)","2010 restated":"(20,000)"},"Other":{"2011":"(25,000)","2010 restated":"(10,000)"},"Depreciation":{"2011":"(10,000)","2010 restated":"(10,000)"}},"Programme costs":{"Impairment loss":{"2011":"(10,000)","2010 restated":"(5,000)"},"Other":{"2011":"(5,000)","2010 restated":"(5,000)"},"Surplus":{"2011":"5,000","2010 restated":"30,000"}}}</table><table><caption>Table 11: self-contained year-end statement (£, thousands) (multiple problems resolved)</caption>{"Income":{"General income":{"2011":"250,000","2010 restated":"200,000"},"Increase in value":{"2011":"15,000","2010 restated":"30,000"},"Total income":{"2011":"265,000","2010 restated":"230,000"}},"Administrative costs":{"Staff costs":{"2011":"(200,000)","2010 restated":"(150,000)"},"Early departures":{"2011":"(10,000)","2010 restated":"(20,000)"},"Other operating costs":{"2011":"(25,000)","2010 restated":"(10,000)"},"Depreciation":{"2011":"(10,000)","2010 restated":"(10,000)"}},"Programme costs":{"Impairment loss":{"2011":"(10,000)","2010 restated":"(5,000)"},"Other":{"2011":"(5,000)","2010 restated":"(5,000)"},"Total costs":{"2011":"(260,000)","2010 restated":"(200,000)"}},"Surplus":{"Surplus":{"2011":"5,000","2010 restated":"30,000"}}}</table><!-- PageBreak -->**Page 4**<table><caption>Table 12: merged data cells are not recommended</caption><title>2008</title>[{"Name":"Bob","Yes":"6","No":"7"},{"Name":"Sue","Yes":"4","No":"7"},{"Name":"Sam","Yes":"[data relating to both columns in a single cell spanning both]","No":"[data relating to both columns in a single cell spanning both]"}]</table><table><caption>Table 13: use of graphic symbols</caption>[{"Question":"Are you a UK citizen?","Respondent A":"Yes","Respondent B":"Yes","Respondent C":"Yes"},{"Question":"Are you currently employed?","Respondent A":"Yes","Respondent B":"Yes","Respondent C":"Yes"},{"Question":"Do you have a driving licence?","Respondent A":"Yes","Respondent B":"Yes","Respondent C":"Yes"}]</table><table><caption>Table 14: symbols replaced by real text</caption>[{"Question":"Are you a UK citizen?","Respondent A":"No","Respondent B":"Yes","Respondent C":"No"},{"Question":"Are you currently employed?","Respondent A":"Yes","Respondent B":"No","Respondent C":"Yes"},{"Question":"Do you have a driving licence?","Respondent A":"No","Respondent B":"No","Respondent C":"Yes"}]</table><table><caption>Table 15: courses offered by Institution X. A = Bachelor of Science, B = Bachelor of Arts, C = Masters, D = Doctorate, E = Diploma</caption>{"Economics":{"2006":"A, B","2007":"A, C","2008":"A, B","2009":"A, C"},"International relations":{"2006":"A, E","2007":"A, E","2008":"A, B","2009":"A, E"},"Philosophy":{"2006":"A","2007":"A","2008":"A","2009":"A, D"},"Politics":{"2006":"A, D","2007":"A, D","2008":"A, D","2009":"A"},"Mathematics":{"2006":"B, C","2007":"B","2008":"A, E","2009":"A, B"},"English":{"2006":"A, C","2007":"A, B","2008":"A,B","2009":"C"}}</table><!-- PageBreak -->**Page 5**<table><caption>Table 16: Masters courses offered by Institution X</caption>{"Economics":{"2006":"No","2007":"Yes","2008":"Yes","2009":"Yes"},"International relations":{"2006":"No","2007":"No","2008":"No","2009":"No"},"Philosophy":{"2006":"No","2007":"No","2008":"No","2009":"No"},"Politics":{"2006":"No","2007":"No","2008":"No","2009":"No"},"Mathematics":{"2006":"Yes","2007":"No","2008":"No","2009":"No"},"English":{"2006":"Yes","2007":"Yes","2008":"Yes","2009":"Yes"}}</table><table><caption>Table 17: accounts, 2011 (£, thousands)</caption>{"Income":{"General income":{"2011":"200,000"},"Increase in value, WIP":{"2011":"30,000"},"Income subtotal":{"2011":"230,000"}},"Administrative costs":{"Staff":{"2011":"150,000"},"Early departures":{"2011":"20,000"},"Other operating costs":{"2011":"10,000"},"Depreciation":{"2011":"10,000"}},"Programme costs":{"Impairment loss":{"2011":"10,000"},"Costs subtotal":{"2011":"200,000"}},"Balance":{"Balance":{"2011":"30,000"}}}</table><table><caption>Table 18: accounts, 2011 (£, thousands)</caption>{"Income":{"General income":{"2011":"200,000"},"Increase in value, WIP":{"2011":"30,000"},"Income subtotal":{"2011":"230,000"}},"Administrative costs":{"Staff":{"2011":"(150,000)"},"Early departures":{"2011":"(20,000)"},"Other operating costs":{"2011":"(10,000)"},"Depreciation":{"2011":"(10,000)"}},"Programme costs":{"Impairment loss":{"2011":"(10,000)"},"Costs subtotal":{"2011":"(200,000)"}},"Balance":{"Balance":{"2011":"30,000"}}}</table><!-- PageBreak -->**Page 6**<table><caption>Table 19: Human Development Index (HDI) trends, 1980 to 2010. Source: Barro-Lee March, 2010</caption>[{"Country":"Afghanistan","1980":"0.78","1990":"1.48","2000":"2.16","2010":"3.33"},{"Country":"Albania","1980":"8.89","1990":"9.67","2000":"9.89","2010":"10.38"},{"Country":"Algeria","1980":"4.74","1990":"3.33","2000":"5.50","2010":"7.24"},{"Country":"Andorra","1980":"4.98","1990":"5.63","2000":"9.09","2010":"10.35"},{"Country":"Angola","1980":"-","1990":"-","2000":"4.42","2010":"4.42"}]</table><table><caption>Table 20: footnotes referenced from within a table</caption>{"Policy functions":{"Financial":{"2009/10":"22.5","2010/11 1":"30.57"},"Information 2":{"2009/10":"10.2","2010/11 1":"14.8"},"Contingency":{"2009/10":"2.6","2010/11 1":"1.2"}},"Remunerated functions":{"Agency services 3":{"2009/10":"44.7","2010/11 1":"35.91"},"Payments":{"2009/10":"22.41","2010/11 1":"19.88"},"Banking":{"2009/10":"22.90","2010/11 1":"44.23"},"Other":{"2009/10":"12.69","2010/11 1":"10.32"}}}</table>(1) Provisional total as of publication date.(2) Costs associated with on-going information programmes.(3) From the management accounts, net of recoveries and including interest charges<!-- PageBreak -->**Page 7**<table><caption>Table 21: footnotes replaced by additional table summary text</caption>{"Policy functions":{"Financial":{"2009/10":"22.5","2010/11":"30.57"},"Information":{"2009/10":"10.2","2010/11":"14.8"},"Contingency":{"2009/10":"2.6","2010/11":"1.2"}},"Remunerated functions":{"Agency services":{"2009/10":"44.7","2010/11":"35.91"},"Payments":{"2009/10":"22.41","2010/11":"19.88"},"Banking":{"2009/10":"22.90","2010/11":"44.23"},"Other":{"2009/10":"12.69","2010/11":"10.32"}}}</table><table><caption>Table 22: referencing multiple endnotes from within a table</caption>[{"Expenditure £m":"Information","Notes (Notes located on page [n])":"1","2010":"10.2","2011":"14.8"},{"Expenditure £m":"Contingency","Notes (Notes located on page [n])":"","2010":"2.6","2011":"1.2"},{"Expenditure £m":"Payments","Notes (Notes located on page [n])":"3","2010":"22.41","2011":"19.88"},{"Expenditure £m":"Banking services","Notes (Notes located on page [n])":"4","2010":"22.90","2011":"44.23"},{"Expenditure £m":"Interest","Notes (Notes located on page [n])":"","2010":"0.23","2011":"0.10"},{"Expenditure £m":"Dividends","Notes (Notes located on page [n])":"23","2010":"2.5","2011":"3.68"},{"Expenditure £m":"Other","Notes (Notes located on page [n])":"9","2010":"12.69","2011":"10.32"}]</table><table><caption>Table 23: simulated table created using tabs and containing no structure</caption><title>2008</title>[{"Name":"Bob","Entered":"20","Completed":"19"},{"Name":"Sue","Entered":"12","Completed":"10"}]</table><!-- PageBreak -->**Page 8**<table><caption>Table 24: year-end financial statement (£, thousands)</caption>{"Non-current assets":{"Buildings":{"2010":"345","2009":"445","2008":"222"},"Investment":{"2010":"567","2009":"654","2008":"423"},"Intangibles":{"2010":"423","2009":"123","2008":"453"}},"Current assets":{"Trade":{"2010":"435","2009":"634","2008":"231"},"Cash":{"2010":"524","2009":"123","2008":"482"},"Other":{"2010":"223","2009":"211","2008":"254"}},"Current liabilities":{"Trade liabilities":{"2010":"154","2009":"125","2008":"421"},"Financial debt":{"2010":"231","2009":"474","2008":"572"},"Provisions":{"2010":"111","2009":"312","2008":"347"}}}</table><table><caption>Table 25: setting column and row scope via the tags panel</caption><title>2008</title>[{"Name":"Bob","Entered":"20","Won":"19"},{"Name":"Sue","Entered":"12","Won":"10"},{"Name":"Sam","Entered":"45","Won":"30"}]</table><table><caption>Table 26: courses offered by Institution X. A = Bachelor of Science, B = Bachelor of Arts, C = Masters, D = Doctorate, E = Diploma</caption>{"Economics":{"2006":"A, B","2007":"A, C","2008":"A, C","2009":"A, C"},"International relations":{"2006":"A, E","2007":"A, E","2008":"A, B","2009":"A, B"},"Philosophy":{"2006":"A","2007":"A","2008":"A","2009":"A"},"Politics":{"2006":"A, D","2007":"A, D","2008":"A, B","2009":"A"},"Mathematics":{"2006":"B, C","2007":"B","2008":"A, B","2009":"A, B"},"English":{"2006":"A, C","2007":"A, B","2008":"A,B","2009":"A, C"}}</table><!-- PageBreak -->**Page 9**<table><caption>Table 27: "table" with columns simulated by using tab stops</caption>[{"Name":"Bob Scott","Apples":"20","Pears":"25"},{"Name":"Susan. P. Arnold-Jones, BA, FRSA, MD","Apples":"24","Pears":"15"},{"Name":"Sam Holder-Dickinson","Apples":"14","Pears":"10"}]</table><table><caption>Table 28: year-end financial table (£, thousands) - headings problem revisited</caption>{"Non-current assets":{"Buildings":{"2010":"345","2009":"445","2008":"222"},"Investment":{"2010":"567","2009":"654","2008":"423"},"Intangibles":{"2010":"423","2009":"123","2008":"453"}},"Current assets":{"Trade":{"2010":"435","2009":"634","2008":"231"},"Cash":{"2010":"524","2009":"123","2008":"482"},"Other":{"2010":"223","2009":"211","2008":"254"}},"Current liabilities":{"Trade liabilities":{"2010":"154","2009":"125","2008":"421"},"Financial debt":{"2010":"231","2009":"474","2008":"572"},"Provisions":{"2010":"111","2009":"312","2008":"347"}}}</table><!-- PageBreak -->**Page 10**<table><caption>Table 29: multiple headers attributes for each data cell</caption>{"2010":{"Highest average":{"Asia":"523.6","Africa":"467.4","Australia":"405.0"},"Highest in 24 hours":{"Asia":"73.1","Africa":"54.1","Australia":"27.2"},"Highest in 12 hours":{"Asia":"42.4","Africa":"30.1","Australia":"15.9"}},"2009":{"Highest average":{"Asia":"487.7","Africa":"453.6","Australia":"398.7"},"Highest in 24 hours":{"Asia":"67.2","Africa":"53.2","Australia":"44.3"},"Highest in 12 hours":{"Asia":"34.7","Africa":"34.1","Australia":"29.8"}},"2008":{"Highest average":{"Asia":"496.7","Africa":"444.3","Australia":"502.1"},"Highest in 24 hours":{"Asia":"44.2","Africa":"56.7","Australia":"32.1"},"Highest in 12 hours":{"Asia":"30.1","Africa":"32.7","Australia":"21.9"}}}</table>**Page 11**', 'language': 'en'}

=== TEST\TEST_SYMBOL.TXT ===
{'content': '<table>[{"Col1":"Cell1_1","Col2":"Yes","Col3":"No","column":"Cell1_2"},{"Col1":"Cell2_1","Col2":"Yes","Col3":"No","column":"Cell2_2"},{"Col1":"Cell3_1","Col2":"Yes","Col3":"No","column":"Cell3_2"},{"Col1":"Cell4_1","Col2":"No","Col3":"Yes","column":"Cell4_2"},{"Col1":"Cell5_1","Col2":"Yes","Col3":"No","column":"Cell5_2"},{"Col1":"Cell6_1","Col2":"Yes","Col3":"No","column":"Cell6_2"}]</table>', 'language': 'en'}

=== TEST\TRIAL1.TXT ===
{'content': '<table>[{"Row":"Row header (TH)","Column header (TH)":"Data cell (TD)"},{"Row":"Row header(TH)","Column header (TH)":"Data cell (TD)"}]</table><br><table>[{"Name":"John","Math":"85","Science":"90"},{"Name":"Mary","Math":"78","Science":"88"},{"Name":"Mike","Math":"92","Science":"95"}]</table><br><table>[{"Q1":"Q2","1200":"1500"},{"Q1":"Q3","1200":"1700"}]</table><br><table>[{"Name":"Alice","Age":"25","City":"New York"},{"Name":"Bob","Age":"30","City":"London"},{"Name":"Charlie","Age":"35","City":"Paris"}]</table><br><table>[{"Item":"Apple","Price":"$1.00","Quantity":"10"},{"Item":"Banana","Price":"$0.50","Quantity":"20"},{"Item":"Orange","Price":"$0.75","Quantity":"15"}]</table><br><table>[{"Product":"Laptop","Category":"Electronics","Stock":"5"},{"Product":"Chair","Category":"Furniture","Stock":"12"},{"Product":"Book","Category":"Education","Stock":"25"}]</table><br><table>{"Marketing":"Development","5000":"10000","7000":"12000"}</table><br><table>{"1":"5","2":"6","3":"7","4":"8"}</table><br><table>[{"Row":"1","Nested Table":"Inner 1"},{"Row":"2","Nested Table":"Inner 2"}]</table><br><table>[{"Country":"USA","Capital":"Washington","Population":"331M"},{"Country":"UK","Capital":"London","Population":"67M"},{"Country":"France","Capital":"Paris","Population":"68M"}]</table><br><table>[{"Student":"John","Grade":"A","Subject":"Math"},{"Student":"Mary","Grade":"B","Subject":"Science"},{"Student":"Mike","Grade":"A","Subject":"History"}]</table><br><table>[{"Company":"Apple","Revenue":"365B","Profit":"95B"},{"Company":"Google","Revenue":"257B","Profit":"76B"},{"Company":"Microsoft","Revenue":"168B","Profit":"61B"}]</table><br><table>[{"Month":"Jan","Sales":"1000","Target":"1200"},{"Month":"Feb","Sales":"1100","Target":"1200"},{"Month":"Mar","Sales":"1300","Target":"1200"}]</table><br><table>[{"Team":"Lakers","Wins":"45","Losses":"37"},{"Team":"Warriors","Wins":"53","Losses":"29"},{"Team":"Celtics","Wins":"51","Losses":"31"}]</table><br><table><title>Name - Math</title>{"Alice":{"Math":"85","Science":"90"}}</table><br><table><title>Sales Report - Q1</title>{"Sales Report":{"Q1":"100","Q2":"150","Q3":"200","Q4":"250"}}</table><br><table>{"Pen":"Notebook","Blue":"200 pages","10 pcs":"5 pcs"}</table><br><table>[{"Event":"Conference","Date":"2024-01-15","Location":"NYC"},{"Event":"Workshop","Date":"2024-02-20","Location":"LA"}]</table><br><table>[{"Item":"Apple","Qty":"10","Price":"$1.00"},{"Item":"Banana","Qty":"5","Price":"$0.50"}]</table><br><table>[{"Course":"Math 101","Credits":"3","Professor":"Dr. Smith"},{"Course":"Physics 201","Credits":"4","Professor":"Dr. Jones"}]</table><br><table>[{"Book":"1984","Author":"Orwell","Year":"1949"},{"Book":"Brave New World","Author":"Huxley","Year":"1932"}]</table><br><table>[{"Language":"Python","Popularity":"High","Usage":"Data Science"},{"Language":"JavaScript","Popularity":"Very High","Usage":"Web Dev"}]</table><br><table>[{"City":"Miami","Temperature":"85°F","Humidity":"70%"},{"City":"Denver","Temperature":"72°F","Humidity":"45%"}]</table><br><table>[{"Animal":"Lion","Habitat":"Savanna","Diet":"Carnivore"},{"Animal":"Elephant","Habitat":"Grassland","Diet":"Herbivore"}]</table><br><table>[{"Device":"iPhone","Brand":"Apple","Price":"$999"},{"Device":"Galaxy","Brand":"Samsung","Price":"$899"}]</table><br><table>[{"Sport":"Basketball","Players":"5","Equipment":"Ball, Hoop"},{"Sport":"Soccer","Players":"11","Equipment":"Ball, Goals"}]</table><br><table>[{"Movie":"Inception","Director":"Nolan","Year":"2010"},{"Movie":"Avatar","Director":"Cameron","Year":"2009"}]</table><br>', 'language': 'en'}

=== TEST\TRIAL2.TXT ===
{'content': '<table><caption>Sales Report - Q1</caption><title>Region - Product A - Product B</title>[{"Region":"North","Product A":"120","Product B":"98"},{"Region":"South","Product A":"140","Product B":"110"},{"Region":"East","Product A":"130","Product B":"105"},{"Region":"West","Product A":"115","Product B":"95"}]</table><br><table><caption>Table 2: Department Budget Allocation</caption>{"Operations":{"Logistics":{"2023":"2.3M","2024":"2.6M"},"Manufacturing":{"2023":"4.5M","2024":"4.8M"}},"Support":{"IT":{"2023":"1.1M","2024":"1.3M"},"HR":{"2023":"900K","2024":"950K"}},"Total":{"Total":{"2023":"8.8M","2024":"9.65M"}}}</table><br><table><caption>Table 3: Weekly Schedule</caption>[{"Day":"Monday","Morning":"Team Meeting","Afternoon":"Client Call","Evening":"Project Review"},{"Day":"Tuesday","Morning":"Development","Afternoon":"QA Testing","Evening":"Planning"},{"Day":"Wednesday","Morning":"Workshop","Afternoon":"Break","Evening":"Design Review"},{"Day":"Thursday","Morning":"Code Review","Afternoon":"Scrum","Evening":"1:1 Meetings"},{"Day":"Friday","Morning":"Deployment","Afternoon":"Retrospective","Evening":"Wrap-up"}]</table><br><table><caption>Table 1</caption>{"Task A":{"status":"In Progress","details":"Due: Friday"}}</table><br><table><caption>Table 2</caption>{"Name":"John Smith","Role":"Designer","Start Date":"01-Feb-2024"}</table><br><table><caption>Table 3</caption>{"Apple":"Red","Banana":"Yellow","Grapes":"Purple"}</table><br><table><caption>Table 4</caption>[{"Service":"API","Status":"Running"},{"Service":"Database","Status":"Offline"},{"Service":"Web","Status":"Running","column":"Cached"}]</table><br><table><caption>Table 5</caption>{"Username":"@mira_x","Followers":"12.5k","Posts":"134"}</table><br><table><caption>Table 6</caption>[{"Stage":"Planning","Date":"Mar 1","Status":"Done"},{"Stage":"Design","Date":"Mar 5","Status":"In Progress"},{"Stage":"Launch","Date":"Apr 1","Status":"Upcoming"}]</table><br><table><caption>Table 7</caption>{"column":"Alpha,Beta,Gamma,Delta,Epsilon"}</table><br><table><caption>Table 8</caption>[{"Country":"Capital","Japan":"Tokyo"},{"Country":"Population","Japan":"125M"}]</table><br><table><caption>Table 9</caption>[{"A1":"B1","A2":"B2","A3":"B3"},{"A1":"C1","A2":"C2","A3":"C3"}]</table><br><table><caption>Table 10</caption>[{"Metric":"Visits","Value":"5,202"},{"Metric":"Conversions","Value":"312"}]</table>'}

=== TEST\TRIAL3.TXT ===
{'content': '<table><caption>Employee Performance Review</caption>[{"Employee ID":"EMP001","Department":"Engineering","Rating":"Excellent"},{"Employee ID":"EMP002","Department":"Marketing","Rating":"Good"},{"Employee ID":"EMP003","Department":"Sales","Rating":"Outstanding"}]</table><br><table><caption>Project Budget Allocation</caption><title>Project</title>[{"Project":"Website Redesign","Allocated":"50000","Spent":"42000"},{"Project":"Mobile App","Allocated":"75000","Spent":"68000"},{"Project":"Database Migration","Allocated":"30000","Spent":"25000"}]</table><br><table><caption>Course Enrollment Statistics</caption>[{"Course Code":"CS101","Course Name":"Introduction to Programming","Enrolled":"45","Capacity":"50"},{"Course Code":"MATH201","Course Name":"Calculus II","Enrolled":"38","Capacity":"40"},{"Course Code":"ENG102","Course Name":"Technical Writing","Enrolled":"32","Capacity":"35"},{"Course Code":"PHYS301","Course Name":"Quantum Mechanics","Enrolled":"28","Capacity":"30"}]</table><br><table><caption>Inventory Status</caption>[{"Item Code":"ITM001","Description":"Laptop Computer","Stock":"25","Status":"Yes"},{"Item Code":"ITM002","Description":"Wireless Mouse","Stock":"150","Status":"Yes"},{"Item Code":"ITM003","Description":"Monitor 24inch","Stock":"8","Status":"No"},{"Item Code":"ITM004","Description":"Keyboard Mechanical","Stock":"45","Status":"Yes"},{"Item Code":"ITM005","Description":"USB Cable","Stock":"200","Status":"Yes"}]</table><br><table><caption>Monthly Sales by Region</caption><title>Month</title>[{"Month":"January","North":"1200","South":"980","West":"1150"},{"Month":"February","North":"1350","South":"1100","West":"1250"},{"Month":"March","North":"1180","South":"1050","West":"1300"}]</table><br><table><caption>Server Performance Metrics</caption>[{"Server Name":"WEB-01","CPU Usage (%)":"45","Memory Usage (%)":"62","Disk Usage (%)":"78","Status":"Yes"},{"Server Name":"DB-01","CPU Usage (%)":"78","Memory Usage (%)":"85","Disk Usage (%)":"45","Status":"Yes"},{"Server Name":"APP-01","CPU Usage (%)":"32","Memory Usage (%)":"48","Disk Usage (%)":"67","Status":"Yes"},{"Server Name":"BACKUP-01","CPU Usage (%)":"15","Memory Usage (%)":"25","Disk Usage (%)":"92","Status":"Yes"}]</table><br><table><caption>Training Program Results</caption>[{"Program":"Safety Training","Participants":"120","Completed":"118","Pass Rate":"98%"},{"Program":"Leadership Development","Participants":"45","Completed":"42","Pass Rate":"93%"},{"Program":"Technical Skills","Participants":"85","Completed":"79","Pass Rate":"93%"},{"Program":"Communication Skills","Participants":"65","Completed":"63","Pass Rate":"97%"}]</table><br><table><caption>Quality Control Checklist</caption>[{"Check Item":"Dimension Check","Batch A":"Yes","Batch B":"Yes","Batch C":"No"},{"Check Item":"Weight Verification","Batch A":"Yes","Batch B":"No","Batch C":"Yes"},{"Check Item":"Color Consistency","Batch A":"Yes","Batch B":"Yes","Batch C":"Yes"},{"Check Item":"Surface Finish","Batch A":"No","Batch B":"Yes","Batch C":"Yes"},{"Check Item":"Packaging Quality","Batch A":"Yes","Batch B":"Yes","Batch C":"No"}]</table><br><table><caption>Financial Summary - Q2</caption>{"Product Sales":{"Hardware":{"Amount (USD)":"125000"},"Software":{"Amount (USD)":"85000"},"Services":{"Amount (USD)":"65000"}},"Consulting":{"Technical":{"Amount (USD)":"45000"},"Business":{"Amount (USD)":"35000"}}}</table>', 'language': 'en'}

=== TEST\TRIAL4.TXT ===
{'content': '<table><caption>Research Study Results</caption><title>Study Group - Sample Size - Success Rate - Confidence Level</title>{"Study Group":"Treatment B","Sample Size":"148","Success Rate":"79%","Confidence Level":"95%"}</table><table><caption>Manufacturing Production Line</caption><title>Production Line</title>[{"Production Line":"Line A","Monday":"450","Tuesday":"480","Wednesday":"465"},{"Production Line":"Line B","Monday":"380","Tuesday":"395","Wednesday":"410"},{"Production Line":"Line C","Monday":"520","Tuesday":"535","Wednesday":"515"}]</table><table><caption>Customer Satisfaction Survey</caption>[{"Service Category":"Customer Support","Excellent":"Neutral","Good":"Yes","Fair":"No","Poor":"No"},{"Service Category":"Product Quality","Excellent":"Yes","Good":"No","Fair":"No","Poor":"No"},{"Service Category":"Delivery Time","Excellent":"No","Good":"Yes","Fair":"No","Poor":"No"},{"Service Category":"Pricing","Excellent":"No","Good":"No","Fair":"Yes","Poor":"No"}]</table><table><caption>Software Development Sprint</caption>[{"Task ID":"TASK-001","Task Description":"User Authentication Module","Assigned To":"Alice Johnson","Status":"Yes","Priority":"High"},{"Task ID":"TASK-002","Task Description":"Database Schema Design","Assigned To":"Bob Smith","Status":"Yes","Priority":"High"},{"Task ID":"TASK-003","Task Description":"API Endpoint Development","Assigned To":"Carol Davis","Status":"No","Priority":"Medium"},{"Task ID":"TASK-004","Task Description":"Frontend UI Components","Assigned To":"David Wilson","Status":"Yes","Priority":"Medium"},{"Task ID":"TASK-005","Task Description":"Testing Framework Setup","Assigned To":"Eve Brown","Status":"No","Priority":"Low"}]</table><table><caption>Energy Consumption Report</caption>{"Office Floors":{"Floor 1-5":{"kWh Used":"2400","Cost (USD)":"360"},"Floor 6-10":{"kWh Used":"2200","Cost (USD)":"330"},"Floor 11-15":{"kWh Used":"1800","Cost (USD)":"270"}},"Common Areas":{"Lobby & Elevators":{"kWh Used":"800","Cost (USD)":"120"},"Parking Garage":{"kWh Used":"600","Cost (USD)":"90"}}}</table><table><caption>Medical Test Results</caption>[{"Patient ID":"P001","Blood Pressure":"120/80","Heart Rate":"72","Temperature":"98.6°F","Normal Range":"Yes"},{"Patient ID":"P002","Blood Pressure":"140/90","Heart Rate":"85","Temperature":"99.2°F","Normal Range":"No"},{"Patient ID":"P003","Blood Pressure":"115/75","Heart Rate":"68","Temperature":"98.4°F","Normal Range":"Yes"},{"Patient ID":"P004","Blood Pressure":"130/85","Heart Rate":"78","Temperature":"98.8°F","Normal Range":"Yes"}]</table><table><caption>Event Planning Checklist</caption>[{"Task Category":"Venue","Task":"Book Conference Hall","Deadline":"2024-06-15","Completed":"Yes"},{"Task Category":"Venue","Task":"Setup Audio/Visual","Deadline":"2024-06-20","Completed":"Yes"},{"Task Category":"Venue","Task":"Arrange Seating","Deadline":"2024-06-22","Completed":"No"},{"Task Category":"Catering","Task":"Select Menu","Deadline":"2024-06-18","Completed":"Yes"},{"Task Category":"Catering","Task":"Confirm Headcount","Deadline":"2024-06-21","Completed":"No"}]</table>', 'language': 'en'}

=== TEST\TRIAL5.TXT ===
{'content': '<table><caption>University Course Schedule</caption><title>Time</title>{"general":{"9:00 AM":{"Monday":"Mathematics","Tuesday":"Physics","Wednesday":"Mathematics","Thursday":"Physics","Friday":"Lab"},"10:00 AM":{"Monday":"Chemistry","Tuesday":"Biology","Wednesday":"Chemistry","Thursday":"Biology","Friday":"Lab"},"11:00 AM":{"Monday":"English","Tuesday":"History","Wednesday":"English","Thursday":"History","Friday":"Free"},"12:00 PM":{"Monday":"Lunch Break","Tuesday":"Lunch Break","Wednesday":"Lunch Break","Thursday":"Lunch Break","Friday":"Lunch Break"},"1:00 PM":{"Monday":"Computer Science","Tuesday":"Art","Wednesday":"Computer Science","Thursday":"Art","Friday":"Free"}}}</table><br><table><caption>Sports Tournament Results</caption><title>Team</title>[{"Team":"Eagles","Wins":"8","Draws":"2","Losses":"0","Points":"26"},{"Team":"Lions","Wins":"6","Draws":"3","Losses":"1","Points":"21"},{"Team":"Tigers","Wins":"5","Draws":"2","Losses":"3","Points":"17"},{"Team":"Bears","Wins":"3","Draws":"4","Losses":"3","Points":"13"},{"Team":"Wolves","Wins":"2","Draws":"1","Losses":"7","Points":"7"}]</table><br><table><caption>Library Book Inventory</caption>[{"Category":"Fiction","Total Books":"2500","Available":"1800","Checked Out":"650","Reserved":"50"},{"Category":"Non-Fiction","Total Books":"1800","Available":"1200","Checked Out":"550","Reserved":"50"},{"Category":"Science","Total Books":"1200","Available":"900","Checked Out":"280","Reserved":"20"},{"Category":"History","Total Books":"800","Available":"600","Checked Out":"180","Reserved":"20"},{"Category":"Children","Total Books":"1500","Available":"1100","Checked Out":"350","Reserved":"50"}]</table><br><table><caption>Restaurant Menu Pricing</caption>{"Appetizers":{"Caesar Salad":{"Price (USD)":"8.50","Popular":"Neutral"},"Chicken Wings":{"Price (USD)":"12.00","Popular":"Yes"},"Mozzarella Sticks":{"Price (USD)":"9.50","Popular":"Yes"},"Soup of the Day":{"Price (USD)":"6.00","Popular":"Neutral"}},"Main Courses":{"Grilled Salmon":{"Price (USD)":"22.00","Popular":"Yes"},"Beef Steak":{"Price (USD)":"28.00","Popular":"Yes"},"Chicken Parmesan":{"Price (USD)":"18.00","Popular":"Neutral"},"Vegetarian Pasta":{"Price (USD)":"16.00","Popular":"Neutral"}},"Desserts":{"Chocolate Cake":{"Price (USD)":"7.50","Popular":"Yes"},"Ice Cream":{"Price (USD)":"5.00","Popular":"Neutral"},"Fruit Tart":{"Price (USD)":"6.50","Popular":"Neutral"}}}</table><br><table><caption>Weather Forecast - Weekly</caption>[{"Day":"Monday","High (°F)":"75","Low (°F)":"58","Condition":"Sunny","Rain Chance":"10%"},{"Day":"Tuesday","High (°F)":"72","Low (°F)":"55","Condition":"Partly Cloudy","Rain Chance":"20%"},{"Day":"Wednesday","High (°F)":"68","Low (°F)":"52","Condition":"Cloudy","Rain Chance":"60%"},{"Day":"Thursday","High (°F)":"65","Low (°F)":"48","Condition":"Rainy","Rain Chance":"80%"},{"Day":"Friday","High (°F)":"70","Low (°F)":"53","Condition":"Partly Cloudy","Rain Chance":"30%"},{"Day":"Saturday","High (°F)":"78","Low (°F)":"62","Condition":"Sunny","Rain Chance":"5%"},{"Day":"Sunday","High (°F)":"80","Low (°F)":"65","Condition":"Sunny","Rain Chance":"5%"}]</table><br><table><caption>Employee Attendance Record</caption>[{"Employee":"Sarah Johnson","Department":"HR","Days Present":"22","Days Absent":"0","Attendance Rate":"100%"},{"Employee":"Mike Chen","Department":"IT","Days Present":"21","Days Absent":"1","Attendance Rate":"95%"},{"Employee":"Lisa Rodriguez","Department":"Finance","Days Present":"20","Days Absent":"2","Attendance Rate":"91%"},{"Employee":"Tom Wilson","Department":"Marketing","Days Present":"19","Days Absent":"3","Attendance Rate":"86%"},{"Employee":"Emma Davis","Department":"Operations","Days Present":"22","Days Absent":"0","Attendance Rate":"100%"}]</table><br><table><caption>Product Quality Assessment</caption>[{"Product Code":"PRD-001","Durability Test":"Yes","Safety Test":"Yes","Performance Test":"Yes","Overall":"Pass"},{"Product Code":"PRD-002","Durability Test":"Yes","Safety Test":"No","Performance Test":"Yes","Overall":"Fail"},{"Product Code":"PRD-003","Durability Test":"No","Safety Test":"Yes","Performance Test":"Yes","Overall":"Fail"},{"Product Code":"PRD-004","Durability Test":"Yes","Safety Test":"Yes","Performance Test":"No","Overall":"Fail"},{"Product Code":"PRD-005","Durability Test":"Yes","Safety Test":"Yes","Performance Test":"Yes","Overall":"Pass"}]</table><br><table><caption>Conference Room Booking</caption><title>Time Slot</title>[{"Time Slot":"9:00-10:00","Room A":"Marketing Team","Room B":"Available","Room C":"Board Meeting","Room D":"Available"},{"Time Slot":"10:00-11:00","Room A":"Available","Room B":"IT Training","Room C":"Board Meeting","Room D":"HR Interview"},{"Time Slot":"11:00-12:00","Room A":"Sales Presentation","Room B":"Available","Room C":"Available","Room D":"Available"},{"Time Slot":"12:00-13:00","Room A":"Lunch Break","Room B":"Lunch Break","Room C":"Lunch Break","Room D":"Lunch Break"},{"Time Slot":"13:00-14:00","Room A":"Available","Room B":"Project Review","Room C":"Client Call","Room D":"Available"},{"Time Slot":"14:00-15:00","Room A":"Team Building","Room B":"Available","Room C":"Available","Room D":"Finance Meeting"}]</table>', 'language': 'en'}

=== TEST\TRIAL6.TXT ===
{'content': '<table><caption>Employee Performance Review</caption>[{"Employee ID":"E001","Name":"Sarah Johnson","Department":"Marketing","Rating":"Excellent","Bonus":"$5000"},{"Employee ID":"E002","Name":"Mike Chen","Department":"Engineering","Rating":"Good","Bonus":"$3000"},{"Employee ID":"E003","Name":"Lisa Brown","Department":"Sales","Rating":"Outstanding","Bonus":"$7000"},{"Employee ID":"E004","Name":"David Wilson","Department":"HR","Rating":"Satisfactory","Bonus":"$2000"}]</table><br><table><caption>Financial Statement - Assets</caption><title>Asset Type</title>[{"Asset Type":"Cash & Equivalents","Amount":"$110,000","%":"14%"},{"Asset Type":"Accounts Receivable","Amount":"$180,000","%":"23%"},{"Asset Type":"Inventory","Amount":"$140,000","%":"18%"},{"Asset Type":"Fixed Assets","Amount":"$350,000","%":"45%"}]</table><br><table><caption>Project Status Dashboard</caption>[{"Project":"Website Redesign","Manager":"Alice Cooper","Status":"Yes Complete","Progress":"100%","Due Date":"2024-01-15"},{"Project":"Mobile App","Manager":"Bob Smith","Status":"No Delayed","Progress":"75%","Due Date":"2024-02-28"},{"Project":"Database Migration","Manager":"Carol Davis","Status":"Yes In Progress","Progress":"60%","Due Date":"2024-03-10"},{"Project":"Security Audit","Manager":"Dan Miller","Status":"Yes On Hold","Progress":"25%","Due Date":"2024-04-01"}]</table><br><table><caption>Quarterly Sales by Region</caption><title>Region - 2024 Quarters - Total</title>{"Region":{"2024 Quarters":{"2024 Quarters":{"2024 Quarters":{"2024 Quarters":{"Total":{"Region":{"Q1":{"Q1":{"Q2":{"Q2":{"Total":{"Region":"Asia Pacific","Units":"1,600","Revenue":"$320K","Total":"$620K"}}}}}}}}}}}}}</table><br><table><caption>Course Enrollment Statistics</caption>[{"Course Code":"CS101","Course Name":"Introduction to Programming","Instructor":"Dr. Anderson","Enrolled":"45","Capacity":"50","Waitlist":"12"},{"Course Code":"MATH201","Course Name":"Calculus II","Instructor":"Prof. Martinez","Enrolled":"38","Capacity":"40","Waitlist":"8"},{"Course Code":"ENG102","Course Name":"Technical Writing","Instructor":"Ms. Thompson","Enrolled":"32","Capacity":"35","Waitlist":"5"},{"Course Code":"PHYS301","Course Name":"Quantum Mechanics","Instructor":"Dr. Kumar","Enrolled":"28","Capacity":"30","Waitlist":"15"}]</table><br><table><caption>Inventory Management Report</caption><title>Product Information</title>[{"SKU":"SKU001","Product Name":"Wireless Headphones","Current Stock":"25","Reorder Level":"10","Status":"Yes In Stock"},{"SKU":"SKU002","Product Name":"Bluetooth Speaker","Current Stock":"8","Reorder Level":"15","Status":"No Low Stock"},{"SKU":"SKU003","Product Name":"USB Cable","Current Stock":"150","Reorder Level":"50","Status":"Yes In Stock"},{"SKU":"SKU004","Product Name":"Power Bank","Current Stock":"3","Reorder Level":"20","Status":"No Critical"}]</table><br><table><caption>Weather Forecast - Weekly</caption>[{"Day":"Monday","Date":"Jan 15","High/Low":"72°F / 58°F","Condition":"Sunny","Precipitation":"0%","Wind":"5 mph"},{"Day":"Tuesday","Date":"Jan 16","High/Low":"68°F / 55°F","Condition":"Partly Cloudy","Precipitation":"10%","Wind":"8 mph"},{"Day":"Wednesday","Date":"Jan 17","High/Low":"65°F / 52°F","Condition":"Cloudy","Precipitation":"30%","Wind":"12 mph"},{"Day":"Thursday","Date":"Jan 18","High/Low":"62°F / 48°F","Condition":"Light Rain","Precipitation":"70%","Wind":"15 mph"},{"Day":"Friday","Date":"Jan 19","High/Low":"70°F / 56°F","Condition":"Sunny","Precipitation":"5%","Wind":"7 mph"}]</table><br><table><caption>Budget Allocation by Department</caption><title>Department</title>[{"Department":"Research & Development","Q1":"$50,000","Q2":"$55,000","Total":"$105,000","% Change":"+8%"},{"Department":"Marketing","Q1":"$30,000","Q2":"$35,000","Total":"$65,000","% Change":"+12%"},{"Department":"Operations","Q1":"$40,000","Q2":"$42,000","Total":"$82,000","% Change":"+5%"},{"Department":"Human Resources","Q1":"$20,000","Q2":"$22,000","Total":"$42,000","% Change":"+10%"}]</table><br><table><caption>Customer Satisfaction Survey Results</caption>[{"Service Area":"Customer Support","Excellent":"45","Good":"32","Fair":"15","Poor":"8","Total Responses":"100"},{"Service Area":"Product Quality","Excellent":"52","Good":"28","Fair":"12","Poor":"8","Total Responses":"100"},{"Service Area":"Delivery Service","Excellent":"38","Good":"35","Fair":"18","Poor":"9","Total Responses":"100"},{"Service Area":"Website Experience","Excellent":"42","Good":"30","Fair":"20","Poor":"8","Total Responses":"100"}]</table><br><table><caption>Technology Stack Comparison</caption>[{"Technology":"React","Performance":"High","Scalability":"Excellent","Learning Curve":"Medium","Community Support":"Excellent","Overall Rating":"9/10"},{"Technology":"Vue.js","Performance":"High","Scalability":"Good","Learning Curve":"Easy","Community Support":"Good","Overall Rating":"8/10"},{"Technology":"Angular","Performance":"High","Scalability":"Excellent","Learning Curve":"Hard","Community Support":"Good","Overall Rating":"7/10"},{"Technology":"Svelte","Performance":"Very High","Scalability":"Good","Learning Curve":"Easy","Community Support":"Growing","Overall Rating":"8/10"}]</table><br><table><caption>Monthly Expense Report</caption>{"Office Expenses":{"Rent":{"January":"$5,000","February":"$5,000","March":"$5,000","Quarter Total":"$15,000"},"Utilities":{"January":"$800","February":"$750","March":"$820","Quarter Total":"$2,370"},"Supplies":{"January":"$300","February":"$450","March":"$380","Quarter Total":"$1,130"}},"Travel":{"Flights":{"January":"$2,200","February":"$1,800","March":"$2,500","Quarter Total":"$6,500"},"Hotels":{"January":"$1,200","February":"$900","March":"$1,400","Quarter Total":"$3,500"}}}</table><br><table><caption>Software License Inventory</caption>[{"Software":"Microsoft Office 365","License Type":"Enterprise","Users":"150","Expiry Date":"2024-12-31","Annual Cost":"$18,000","Renewal Status":"Yes Auto-Renew"},{"Software":"Adobe Creative Suite","License Type":"Team","Users":"25","Expiry Date":"2024-06-15","Annual Cost":"$15,000","Renewal Status":"Yes Manual"},{"Software":"Slack Business+","License Type":"Annual","Users":"100","Expiry Date":"2024-09-30","Annual Cost":"$8,000","Renewal Status":"Yes Auto-Renew"},{"Software":"Zoom Pro","License Type":"Monthly","Users":"75","Expiry Date":"2024-03-01","Annual Cost":"$1,200","Renewal Status":"Yes Review Needed"}]</table><br>', 'language': 'en'}

=== TEST\TRIAL7.TXT ===
{'content': '<table><caption>University Course Schedule</caption><title>Time</title>{"general":{"9:00 AM":{"Monday":"Math 101","Tuesday":"Physics 201","Wednesday":"Math 101","Thursday":"Physics 201","Friday":"Lab Session"},"10:30 AM":{"Monday":"Chemistry 150","Tuesday":"English 102","Wednesday":"Chemistry 150","Thursday":"English 102","Friday":"Study Hall"},"12:00 PM":{"Monday":"Lunch Break","Tuesday":"Lunch Break","Wednesday":"Lunch Break","Thursday":"Lunch Break","Friday":"Lunch Break"},"1:30 PM":{"Monday":"History 201","Tuesday":"Biology 101","Wednesday":"History 201","Thursday":"Biology 101","Friday":"Free Period"},"3:00 PM":{"Monday":"Computer Science","Tuesday":"Art 101","Wednesday":"Computer Science","Thursday":"Art 101","Friday":"Sports"}}}</table><br><table><caption>Product Comparison Matrix</caption><title>Feature</title>[{"Feature":"Storage Capacity","Basic":"128GB","Premium":"512GB","Enterprise":"2TB"},{"Feature":"RAM","Basic":"8GB","Premium":"16GB","Enterprise":"32GB"},{"Feature":"Processor","Basic":"Dual Core","Premium":"Quad Core","Enterprise":"Octa Core"},{"Feature":"Graphics","Basic":"Integrated","Premium":"Dedicated 4GB","Enterprise":"Dedicated 8GB"},{"Feature":"Warranty","Basic":"1 Year","Premium":"2 Years","Enterprise":"3 Years"},{"Feature":"Price","Basic":"$599","Premium":"$999","Enterprise":"$1,599"}]</table><br><table><caption>Quality Control Checklist</caption>[{"Item":"Dimensions","Specification":"10cm x 5cm x 2cm","Batch A":"Yes Pass","Batch B":"Yes Pass","Batch C":"No Fail","Status":"Review Required"},{"Item":"Weight","Specification":"150g ± 5g","Batch A":"Yes Pass","Batch B":"No Fail","Batch C":"Yes Pass","Status":"Batch B Rejected"},{"Item":"Color Match","Specification":"Pantone 286C","Batch A":"Yes Perfect","Batch B":"Yes Perfect","Batch C":"Yes Acceptable","Status":"All Approved"},{"Item":"Durability","Specification":"1000 cycles","Batch A":"Yes Pass","Batch B":"Yes Pass","Batch C":"Yes Pass","Status":"All Approved"},{"Item":"Finish Quality","Specification":"Grade A","Batch A":"Yes Grade B","Batch B":"Yes Grade A","Batch C":"Yes Grade A","Status":"Minor Issues"}]</table><br><table><caption>Annual Revenue Breakdown</caption><title>Business Unit - Quarterly Revenue (in millions) - Annual Total - Growth %</title>[{"Business Unit":"Business Unit","Quarterly Revenue (in millions)":"Second Half","Annual Total":"Annual Total","Growth %":"Growth %"},{"Business Unit":"Business Unit","Quarterly Revenue (in millions)":"Q4","Annual Total":"Annual Total","Growth %":"Growth %"},{"Business Unit":"Software Division","Quarterly Revenue (in millions)":"$18.3","Annual Total":"$61.8","Growth %":"+15%"},{"Business Unit":"Hardware Division","Quarterly Revenue (in millions)":"$11.2","Annual Total":"$39.1","Growth %":"+12%"},{"Business Unit":"Services Division","Quarterly Revenue (in millions)":"$8.1","Annual Total":"$27.4","Growth %":"+18%"},{"Business Unit":"Consulting","Quarterly Revenue (in millions)":"$4.5","Annual Total":"$15.6","Growth %":"+22%"}]</table><br><table><caption>Employee Training Matrix</caption>[{"Employee":"John Smith","Safety Training":"Yes Complete","Technical Skills":"Yes In Progress","Leadership":"No Not Started","Compliance":"Yes Complete","Overall Status":"75% Complete"},{"Employee":"Sarah Johnson","Safety Training":"Yes Complete","Technical Skills":"Yes Complete","Leadership":"Yes Complete","Compliance":"Yes Complete","Overall Status":"100% Complete"},{"Employee":"Mike Davis","Safety Training":"Yes In Progress","Technical Skills":"No Not Started","Leadership":"No Not Started","Compliance":"No Overdue","Overall Status":"25% Complete"},{"Employee":"Lisa Chen","Safety Training":"Yes Complete","Technical Skills":"Yes Complete","Leadership":"Yes In Progress","Compliance":"Yes Complete","Overall Status":"90% Complete"}]</table><br><table><caption>Server Performance Metrics</caption>[{"Server":"Web-01","CPU Usage":"45%","Memory Usage":"62%","Disk I/O":"Low","Network":"Normal","Uptime":"99.9%","Status":"Yes Healthy"},{"Server":"Web-02","CPU Usage":"78%","Memory Usage":"85%","Disk I/O":"High","Network":"High","Uptime":"98.5%","Status":"⚠️ Warning"},{"Server":"DB-01","CPU Usage":"32%","Memory Usage":"71%","Disk I/O":"Medium","Network":"Normal","Uptime":"99.8%","Status":"Yes Healthy"},{"Server":"DB-02","CPU Usage":"89%","Memory Usage":"92%","Disk I/O":"Very High","Network":"Critical","Uptime":"95.2%","Status":"No Critical"},{"Server":"Cache-01","CPU Usage":"25%","Memory Usage":"48%","Disk I/O":"Low","Network":"Low","Uptime":"99.9%","Status":"Yes Healthy"}]</table><br><table><caption>Marketing Campaign Results</caption><title>Campaign Details</title>[{"Campaign":"Summer Sale 2024","Channel":"Social Media","Impressions":"2,500,000","Clicks":"125,000","Conversions":"3,750","ROI":"285%"},{"Campaign":"Product Launch","Channel":"Email","Impressions":"500,000","Clicks":"45,000","Conversions":"2,250","ROI":"320%"},{"Campaign":"Brand Awareness","Channel":"Display Ads","Impressions":"1,800,000","Clicks":"54,000","Conversions":"1,080","ROI":"150%"},{"Campaign":"Retargeting","Channel":"Google Ads","Impressions":"800,000","Clicks":"64,000","Conversions":"4,800","ROI":"450%"}]</table><br><table><caption>Laboratory Test Results</caption>[{"Sample ID":"LAB-001","Test Type":"pH Level","Expected Range":"6.5 - 7.5","Actual Value":"7.2","Result":"Yes Within Range","Notes":"Normal"},{"Sample ID":"LAB-002","Test Type":"Temperature","Expected Range":"20°C - 25°C","Actual Value":"28°C","Result":"No Out of Range","Notes":"Too High"},{"Sample ID":"LAB-003","Test Type":"Pressure","Expected Range":"1.0 - 1.5 bar","Actual Value":"1.3 bar","Result":"Yes Within Range","Notes":"Optimal"},{"Sample ID":"LAB-004","Test Type":"Conductivity","Expected Range":"100 - 200 μS/cm","Actual Value":"85 μS/cm","Result":"No Below Range","Notes":"Investigate"},{"Sample ID":"LAB-005","Test Type":"Turbidity","Expected Range":"< 5 NTU","Actual Value":"3.2 NTU","Result":"Yes Excellent","Notes":"Clear"}]</table><br><table><caption>Project Resource Allocation</caption><title>Project Phase</title>[{"Project Phase":"Planning","Developers":"2","Designers":"1","Testers":"0","Budget":"$15,000","Duration":"2 weeks"},{"Project Phase":"Design","Developers":"1","Designers":"3","Testers":"0","Budget":"$25,000","Duration":"4 weeks"},{"Project Phase":"Development","Developers":"8","Designers":"1","Testers":"2","Budget":"$80,000","Duration":"12 weeks"},{"Project Phase":"Testing","Developers":"2","Designers":"0","Testers":"4","Budget":"$20,000","Duration":"3 weeks"},{"Project Phase":"Deployment","Developers":"3","Designers":"0","Testers":"1","Budget":"$10,000","Duration":"1 week"}]</table><br><table><caption>Customer Demographics Analysis</caption>[{"Age Group":"18-25","Male":"1,250","Female":"1,480","Other":"45","Total":"2,775","Percentage":"22.5%","Primary Interest":"Technology"},{"Age Group":"26-35","Male":"1,890","Female":"2,120","Other":"65","Total":"4,075","Percentage":"33.0%","Primary Interest":"Career"},{"Age Group":"36-45","Male":"1,560","Female":"1,720","Other":"35","Total":"3,315","Percentage":"26.8%","Primary Interest":"Family"},{"Age Group":"46-55","Male":"980","Female":"1,150","Other":"25","Total":"2,155","Percentage":"17.4%","Primary Interest":"Health"},{"Age Group":"56+","Male":"180","Female":"210","Other":"8","Total":"398","Percentage":"3.2%","Primary Interest":"Leisure"}]</table><br><table><caption>Security Incident Report</caption>[{"Incident ID":"SEC-2024-001","Date":"2024-01-15","Type":"Malware Detection","Severity":"High","Status":"Yes Resolved","Assigned To":"Security Team","Resolution Time":"4 hours"},{"Incident ID":"SEC-2024-002","Date":"2024-01-18","Type":"Phishing Attempt","Severity":"Medium","Status":"Yes Resolved","Assigned To":"IT Support","Resolution Time":"2 hours"},{"Incident ID":"SEC-2024-003","Date":"2024-01-22","Type":"Unauthorized Access","Severity":"Critical","Status":"Yes In Progress","Assigned To":"Security Team","Resolution Time":"Ongoing"},{"Incident ID":"SEC-2024-004","Date":"2024-01-25","Type":"Data Breach","Severity":"Critical","Status":"No Open","Assigned To":"Incident Response","Resolution Time":"Pending"}]</table><br>', 'language': 'en'}

=== TEST\TRIAL8.TXT ===
{'content': '<table><caption>Manufacturing Production Schedule</caption><title>Product Line</title>[{"Product Line":"Electronics","Mon-Wed":"550","Thu-Fri":"350","Weekend":"0","Total Units":"1,700"},{"Product Line":"Automotive","Mon-Wed":"220","Thu-Fri":"180","Weekend":"120","Total Units":"970"},{"Product Line":"Textiles","Mon-Wed":"850","Thu-Fri":"650","Weekend":"250","Total Units":"3,350"},{"Product Line":"Furniture","Mon-Wed":"160","Thu-Fri":"120","Weekend":"60","Total Units":"640"}]</table><br><table><caption>Clinical Trial Results</caption>[{"Patient ID":"P001","Age":"45","Gender":"F","Treatment Group":"Treatment A","Baseline Score":"85","Week 4 Score":"78","Week 8 Score":"72","Improvement":"Yes Significant"},{"Patient ID":"P002","Age":"52","Gender":"M","Treatment Group":"Placebo","Baseline Score":"82","Week 4 Score":"81","Week 8 Score":"80","Improvement":"No Minimal"},{"Patient ID":"P003","Age":"38","Gender":"F","Treatment Group":"Treatment B","Baseline Score":"90","Week 4 Score":"85","Week 8 Score":"75","Improvement":"Yes Excellent"},{"Patient ID":"P004","Age":"61","Gender":"M","Treatment Group":"Treatment A","Baseline Score":"88","Week 4 Score":"82","Week 8 Score":"76","Improvement":"Yes Significant"},{"Patient ID":"P005","Age":"29","Gender":"F","Treatment Group":"Treatment B","Baseline Score":"92","Week 4 Score":"88","Week 8 Score":"78","Improvement":"Yes Excellent"}]</table><br><table><caption>Network Infrastructure Status</caption>{"Core Switches":{"Switch-01":{"Status":"Yes Online","Uptime":"99.98%","Bandwidth Usage":"65%","Last Maintenance":"2024-01-10"},"Switch-02":{"Status":"Yes Online","Uptime":"99.95%","Bandwidth Usage":"72%","Last Maintenance":"2024-01-10"},"Switch-03":{"Status":"⚠️ Warning","Uptime":"98.50%","Bandwidth Usage":"89%","Last Maintenance":"2023-12-15"}},"Routers":{"Router-Main":{"Status":"Yes Online","Uptime":"99.99%","Bandwidth Usage":"45%","Last Maintenance":"2024-01-05"},"Router-Backup":{"Status":"Yes Standby","Uptime":"100%","Bandwidth Usage":"0%","Last Maintenance":"2024-01-05"}},"Firewalls":{"FW-Primary":{"Status":"Yes Online","Uptime":"99.97%","Bandwidth Usage":"78%","Last Maintenance":"2024-01-08"},"FW-Secondary":{"Status":"No Offline","Uptime":"0%","Bandwidth Usage":"0%","Last Maintenance":"2023-11-20"}}}</table><br><table><caption>Academic Performance by Subject</caption><title>Student</title>[{"Student":"Emma Wilson","Mathematics":"A+ (98)","Science":"A (94)","English":"A- (91)","History":"A (95)","GPA":"3.92","Class Rank":"1st"},{"Student":"James Rodriguez","Mathematics":"A (93)","Science":"A+ (97)","English":"B+ (88)","History":"A- (90)","GPA":"3.85","Class Rank":"2nd"},{"Student":"Sophia Chen","Mathematics":"A- (90)","Science":"A (94)","English":"A+ (99)","History":"A (92)","GPA":"3.88","Class Rank":"3rd"},{"Student":"Michael Johnson","Mathematics":"B+ (87)","Science":"A- (91)","English":"A (93)","History":"B+ (89)","GPA":"3.65","Class Rank":"8th"},{"Student":"Isabella Davis","Mathematics":"A (95)","Science":"A- (90)","English":"A (94)","History":"A+ (98)","GPA":"3.90","Class Rank":"4th"}]</table><br><table><caption>Supply Chain Logistics Report</caption>[{"Supplier":"TechCorp Ltd","Product Category":"Electronics","Order Status":"Yes Delivered","Delivery Date":"2024-01-20","Quality Rating":"A+","Cost per Unit":"$45.50","Reliability":"Yes Excellent"},{"Supplier":"MetalWorks Inc","Product Category":"Raw Materials","Order Status":"Yes In Transit","Delivery Date":"2024-01-25","Quality Rating":"A","Cost per Unit":"$12.30","Reliability":"Yes Good"},{"Supplier":"PlasticPro","Product Category":"Components","Order Status":"No Delayed","Delivery Date":"2024-01-30","Quality Rating":"B+","Cost per Unit":"$8.75","Reliability":"⚠️ Fair"},{"Supplier":"GlobalShip Co","Product Category":"Packaging","Order Status":"Yes Delivered","Delivery Date":"2024-01-18","Quality Rating":"A-","Cost per Unit":"$2.15","Reliability":"Yes Excellent"},{"Supplier":"ChemSupply","Product Category":"Chemicals","Order Status":"Yes Cancelled","Delivery Date":"N/A","Quality Rating":"C","Cost per Unit":"$25.80","Reliability":"No Poor"}]</table><br><table><caption>Energy Consumption Analysis</caption><title>Building</title>[{"Building":"Main Office","January":"15,500","February":"14,200","March":"13,800","Average":"14,500","Cost":"$1,740","Efficiency Rating":"B+"},{"Building":"Warehouse A","January":"8,200","February":"7,800","March":"8,500","Average":"8,167","Cost":"$980","Efficiency Rating":"A-"},{"Building":"Warehouse B","January":"9,100","February":"8,900","March":"9,300","Average":"9,100","Cost":"$1,092","Efficiency Rating":"B"},{"Building":"Manufacturing","January":"25,000","February":"26,500","March":"24,200","Average":"25,233","Cost":"$3,028","Efficiency Rating":"C+"},{"Building":"R&D Lab","January":"3,200","February":"3,100","March":"3,400","Average":"3,233","Cost":"$388","Efficiency Rating":"A"}]</table><br><table><caption>Software Development Sprint Report</caption>[{"Task ID":"TASK-101","Feature":"User Authentication","Assigned Developer":"Alice Johnson","Story Points":"8","Status":"Yes Complete","Completion %":"100%","Blockers":"None"},{"Task ID":"TASK-102","Feature":"Payment Gateway","Assigned Developer":"Bob Smith","Story Points":"13","Status":"Yes In Progress","Completion %":"75%","Blockers":"API Issues"},{"Task ID":"TASK-103","Feature":"Dashboard UI","Assigned Developer":"Carol Davis","Story Points":"5","Status":"Yes Complete","Completion %":"100%","Blockers":"None"},{"Task ID":"TASK-104","Feature":"Data Export","Assigned Developer":"David Wilson","Story Points":"8","Status":"No Blocked","Completion %":"25%","Blockers":"Database Access"},{"Task ID":"TASK-105","Feature":"Mobile Responsive","Assigned Developer":"Eva Martinez","Story Points":"3","Status":"Yes Testing","Completion %":"90%","Blockers":"Browser Compatibility"}]</table><br><table><caption>Hospital Patient Statistics</caption>[{"Department":"Emergency","Current Patients":"45","Capacity":"50","Occupancy Rate":"90%","Average Stay":"4 hours","Discharge Rate":"95%","Status":"Yes Normal"},{"Department":"ICU","Current Patients":"28","Capacity":"30","Occupancy Rate":"93%","Average Stay":"5.2 days","Discharge Rate":"78%","Status":"⚠️ High"},{"Department":"Surgery","Current Patients":"15","Capacity":"20","Occupancy Rate":"75%","Average Stay":"2.1 days","Discharge Rate":"88%","Status":"Yes Good"},{"Department":"Pediatrics","Current Patients":"22","Capacity":"25","Occupancy Rate":"88%","Average Stay":"3.5 days","Discharge Rate":"92%","Status":"Yes Normal"},{"Department":"Maternity","Current Patients":"18","Capacity":"20","Occupancy Rate":"90%","Average Stay":"2.8 days","Discharge Rate":"98%","Status":"Yes Excellent"}]</table><br><table><caption>Investment Portfolio Performance</caption>[{"Asset Class":"Large Cap Stocks","Allocation %":"40%","Current Value":"$400,000","YTD Return":"+12.5%","Risk Level":"Medium","Performance":"Yes Outperforming","Recommendation":"Hold"},{"Asset Class":"International Stocks","Allocation %":"20%","Current Value":"$200,000","YTD Return":"****%","Risk Level":"High","Performance":"Yes Meeting Target","Recommendation":"Hold"},{"Asset Class":"Bonds","Allocation %":"25%","Current Value":"$250,000","YTD Return":"****%","Risk Level":"Low","Performance":"Yes Stable","Recommendation":"Maintain"},{"Asset Class":"Real Estate","Allocation %":"10%","Current Value":"$100,000","YTD Return":"+15.8%","Risk Level":"Medium","Performance":"Yes Excellent","Recommendation":"Increase"},{"Asset Class":"Commodities","Allocation %":"5%","Current Value":"$50,000","YTD Return":"-2.3%","Risk Level":"High","Performance":"No Underperforming","Recommendation":"Review"}]</table><br><table><caption>Website Analytics Dashboard</caption>[{"Metric":"Unique Visitors","This Month":"125,000","Last Month":"118,000","Change":"****%","Target":"120,000","Status":"Yes Above Target","Trend":"📈 Up"},{"Metric":"Page Views","This Month":"450,000","Last Month":"425,000","Change":"****%","Target":"400,000","Status":"Yes Excellent","Trend":"📈 Up"},{"Metric":"Bounce Rate","This Month":"35%","Last Month":"38%","Change":"-3%","Target":"30%","Status":"Yes Improving","Trend":"📉 Down"},{"Metric":"Conversion Rate","This Month":"2.8%","Last Month":"2.5%","Change":"+0.3%","Target":"3.0%","Status":"⚠️ Below Target","Trend":"📈 Up"},{"Metric":"Average Session","This Month":"3m 45s","Last Month":"3m 20s","Change":"+25s","Target":"4m 00s","Status":"Yes Good","Trend":"📈 Up"}]</table><br><table><caption>Environmental Impact Assessment</caption>[{"Impact Category":"CO2 Emissions","Current Level":"850 tons/year","Regulatory Limit":"1000 tons/year","Compliance Status":"Yes Compliant","Trend":"Decreasing","Action Required":"Continue Monitoring","Priority":"Low"},{"Impact Category":"Water Usage","Current Level":"2.5M gallons/month","Regulatory Limit":"3M gallons/month","Compliance Status":"Yes Within Limits","Trend":"Stable","Action Required":"Efficiency Program","Priority":"Medium"},{"Impact Category":"Waste Generation","Current Level":"45 tons/month","Regulatory Limit":"50 tons/month","Compliance Status":"Yes Good","Trend":"Decreasing","Action Required":"Recycling Initiative","Priority":"Low"},{"Impact Category":"Noise Levels","Current Level":"68 dB","Regulatory Limit":"70 dB","Compliance Status":"Yes Acceptable","Trend":"Increasing","Action Required":"Sound Barriers","Priority":"Medium"},{"Impact Category":"Air Quality","Current Level":"PM2.5: 18 μg/m³","Regulatory Limit":"PM2.5: 25 μg/m³","Compliance Status":"Yes Excellent","Trend":"Improving","Action Required":"Maintain Standards","Priority":"Low"}]</table><br>', 'language': 'en'}

=== TEST\TRIAL9.TXT ===
{'content': '<table><caption>Global Sales Performance by Region</caption><title>Region - Sales Data (in thousands USD)</title>[{"Region":"Region","Sales Data (in thousands USD)":"Q3 2024"},{"Region":"Region","Sales Data (in thousands USD)":"Actual"},{"Region":"North America","Sales Data (in thousands USD)":"$580"},{"Region":"Europe","Sales Data (in thousands USD)":"$440"},{"Region":"Asia Pacific","Sales Data (in thousands USD)":"$395"},{"Region":"Latin America","Sales Data (in thousands USD)":"$225"},{"Region":"Middle East & Africa","Sales Data (in thousands USD)":"$165"}]</table><br><table><caption>Research & Development Project Status</caption>[{"Project Code":"RD-2024-001","Project Name":"Quantum Computing Chip","Lead Scientist":"Dr. Sarah Kim","Phase":"Phase III","Progress":"85%","Budget Used":"$2.8M / $3.2M","Status":"Yes On Track","Risk Level":"Low"},{"Project Code":"RD-2024-002","Project Name":"AI Neural Network","Lead Scientist":"Prof. Michael Zhang","Phase":"Phase II","Progress":"60%","Budget Used":"$1.5M / $2.0M","Status":"Yes Progressing","Risk Level":"Medium"},{"Project Code":"RD-2024-003","Project Name":"Renewable Energy Storage","Lead Scientist":"Dr. Elena Rodriguez","Phase":"Phase I","Progress":"30%","Budget Used":"$800K / $2.5M","Status":"⚠️ Behind Schedule","Risk Level":"High"},{"Project Code":"RD-2024-004","Project Name":"Biomedical Sensors","Lead Scientist":"Dr. James Wilson","Phase":"Phase IV","Progress":"95%","Budget Used":"$3.1M / $3.0M","Status":"No Over Budget","Risk Level":"High"},{"Project Code":"RD-2024-005","Project Name":"Smart Materials","Lead Scientist":"Dr. Lisa Chen","Phase":"Phase II","Progress":"45%","Budget Used":"$900K / $1.8M","Status":"Yes Stable","Risk Level":"Low"}]</table><br><table><caption>IT Security Compliance Audit</caption>[{"Security Domain":"Access Management","Total Controls":"25","Implemented":"22","Partially Implemented":"2","Not Implemented":"1","Compliance %":"88%","Risk Rating":"Yes Low"},{"Security Domain":"Data Protection","Total Controls":"30","Implemented":"28","Partially Implemented":"1","Not Implemented":"1","Compliance %":"93%","Risk Rating":"Yes Low"},{"Security Domain":"Network Security","Total Controls":"20","Implemented":"15","Partially Implemented":"3","Not Implemented":"2","Compliance %":"75%","Risk Rating":"⚠️ Medium"},{"Security Domain":"Incident Response","Total Controls":"15","Implemented":"12","Partially Implemented":"2","Not Implemented":"1","Compliance %":"80%","Risk Rating":"Yes Medium"},{"Security Domain":"Business Continuity","Total Controls":"18","Implemented":"10","Partially Implemented":"4","Not Implemented":"4","Compliance %":"56%","Risk Rating":"No High"},{"Security Domain":"Vendor Management","Total Controls":"12","Implemented":"8","Partially Implemented":"2","Not Implemented":"2","Compliance %":"67%","Risk Rating":"⚠️ Medium"}]</table><br><table><caption>Manufacturing Quality Control Dashboard</caption>{"Assembly Line A":{"Shift 1":{"Units Produced":"1,250","Defect Rate":"0.8%","Quality Score":"98.5%","Efficiency":"95%","Status":"Yes Excellent"},"Shift 2":{"Units Produced":"1,180","Defect Rate":"1.2%","Quality Score":"97.8%","Efficiency":"92%","Status":"Yes Good"},"Shift 3":{"Units Produced":"1,100","Defect Rate":"1.8%","Quality Score":"96.5%","Efficiency":"88%","Status":"Yes Acceptable"},"Weekend":{"Units Produced":"800","Defect Rate":"2.1%","Quality Score":"95.2%","Efficiency":"85%","Status":"⚠️ Review Needed"}},"Assembly Line B":{"Shift 1":{"Units Produced":"950","Defect Rate":"1.5%","Quality Score":"97.2%","Efficiency":"90%","Status":"Yes Good"},"Shift 2":{"Units Produced":"920","Defect Rate":"1.9%","Quality Score":"96.8%","Efficiency":"87%","Status":"Yes Acceptable"},"Shift 3":{"Units Produced":"880","Defect Rate":"2.5%","Quality Score":"94.8%","Efficiency":"82%","Status":"No Needs Attention"}}}</table><br><table><caption>Employee Satisfaction Survey Results</caption>[{"Department":"Engineering","Response Rate":"92%","Job Satisfaction":"4.2/5","Work-Life Balance":"3.8/5","Career Development":"4.0/5","Management":"4.1/5","Overall Score":"4.0/5"},{"Department":"Sales","Response Rate":"88%","Job Satisfaction":"3.9/5","Work-Life Balance":"3.5/5","Career Development":"3.7/5","Management":"3.8/5","Overall Score":"3.7/5"},{"Department":"Marketing","Response Rate":"95%","Job Satisfaction":"4.1/5","Work-Life Balance":"4.2/5","Career Development":"3.9/5","Management":"4.0/5","Overall Score":"4.1/5"},{"Department":"HR","Response Rate":"100%","Job Satisfaction":"4.3/5","Work-Life Balance":"4.5/5","Career Development":"4.2/5","Management":"4.4/5","Overall Score":"4.4/5"},{"Department":"Finance","Response Rate":"85%","Job Satisfaction":"3.8/5","Work-Life Balance":"3.6/5","Career Development":"3.5/5","Management":"3.7/5","Overall Score":"3.7/5"},{"Department":"Operations","Response Rate":"90%","Job Satisfaction":"3.7/5","Work-Life Balance":"3.4/5","Career Development":"3.6/5","Management":"3.8/5","Overall Score":"3.6/5"}]</table><br><table><caption>Financial Risk Assessment Matrix</caption>[{"Risk Category":"Market Volatility","Probability":"High","Impact":"High","Risk Score":"9","Current Controls":"Diversification, Hedging","Mitigation Status":"Yes Adequate","Owner":"CFO"},{"Risk Category":"Credit Risk","Probability":"Medium","Impact":"High","Risk Score":"6","Current Controls":"Credit Checks, Insurance","Mitigation Status":"Yes Good","Owner":"Credit Manager"},{"Risk Category":"Operational Risk","Probability":"Medium","Impact":"Medium","Risk Score":"4","Current Controls":"Process Controls, Training","Mitigation Status":"Yes Improving","Owner":"COO"},{"Risk Category":"Regulatory Risk","Probability":"Low","Impact":"High","Risk Score":"3","Current Controls":"Compliance Team, Monitoring","Mitigation Status":"Yes Strong","Owner":"Legal Counsel"},{"Risk Category":"Technology Risk","Probability":"High","Impact":"Medium","Risk Score":"6","Current Controls":"Backup Systems, Updates","Mitigation Status":"⚠️ Needs Improvement","Owner":"CTO"},{"Risk Category":"Liquidity Risk","Probability":"Low","Impact":"Medium","Risk Score":"2","Current Controls":"Cash Reserves, Credit Lines","Mitigation Status":"Yes Excellent","Owner":"Treasurer"}]</table><br><table><caption>Customer Service Performance Metrics</caption>[{"Channel":"Phone Support","Total Tickets":"2,450","Resolved":"2,380","Avg Response Time":"45 seconds","Avg Resolution Time":"8.5 minutes","Customer Rating":"4.2/5","SLA Compliance":"Yes 97%"},{"Channel":"Email Support","Total Tickets":"1,850","Resolved":"1,820","Avg Response Time":"2.5 hours","Avg Resolution Time":"18 hours","Customer Rating":"4.0/5","SLA Compliance":"Yes 92%"},{"Channel":"Live Chat","Total Tickets":"3,200","Resolved":"3,150","Avg Response Time":"30 seconds","Avg Resolution Time":"12 minutes","Customer Rating":"4.3/5","SLA Compliance":"Yes 98%"},{"Channel":"Social Media","Total Tickets":"680","Resolved":"665","Avg Response Time":"1.2 hours","Avg Resolution Time":"4.5 hours","Customer Rating":"3.9/5","SLA Compliance":"Yes 89%"},{"Channel":"Self-Service Portal","Total Tickets":"5,500","Resolved":"4,950","Avg Response Time":"Immediate","Avg Resolution Time":"Self-Resolved","Customer Rating":"3.8/5","SLA Compliance":"Yes 90%"}]</table><br><table><caption>Product Development Roadmap</caption><title>Product</title>[{"Product":"Smart Watch Pro","Concept":"Yes Complete","Design":"Yes Complete","Development":"Yes In Progress","Testing":"No Pending","Launch Date":"Q3 2024","Market Priority":"High"},{"Product":"Wireless Earbuds 2.0","Concept":"Yes Complete","Design":"Yes Complete","Development":"Yes Complete","Testing":"Yes In Progress","Launch Date":"Q2 2024","Market Priority":"Critical"},{"Product":"Fitness Tracker Lite","Concept":"Yes Complete","Design":"Yes In Progress","Development":"No Pending","Testing":"No Pending","Launch Date":"Q4 2024","Market Priority":"Medium"},{"Product":"Smart Home Hub","Concept":"Yes In Progress","Design":"No Pending","Development":"No Pending","Testing":"No Pending","Launch Date":"Q1 2025","Market Priority":"Low"},{"Product":"VR Headset","Concept":"Yes Complete","Design":"Yes Complete","Development":"Yes Complete","Testing":"Yes Complete","Launch Date":"Q1 2024","Market Priority":"Critical"}]</table><br><table><caption>Supply Chain Risk Analysis</caption>[{"Supplier":"TechComponents Asia","Category":"Electronics","Geographic Risk":"Medium","Financial Stability":"High","Quality Risk":"Low","Delivery Risk":"Medium","Overall Risk":"Medium","Backup Plan":"Yes Available"},{"Supplier":"MetalSupply Europe","Category":"Raw Materials","Geographic Risk":"Low","Financial Stability":"High","Quality Risk":"Low","Delivery Risk":"Low","Overall Risk":"Low","Backup Plan":"Yes Multiple Options"},{"Supplier":"PlasticCorp USA","Category":"Components","Geographic Risk":"Low","Financial Stability":"Medium","Quality Risk":"Medium","Delivery Risk":"Low","Overall Risk":"Low","Backup Plan":"Yes Available"},{"Supplier":"ChipMaker Taiwan","Category":"Semiconductors","Geographic Risk":"High","Financial Stability":"High","Quality Risk":"Low","Delivery Risk":"High","Overall Risk":"High","Backup Plan":"⚠️ Limited"},{"Supplier":"PackagePro Global","Category":"Packaging","Geographic Risk":"Medium","Financial Stability":"Medium","Quality Risk":"Medium","Delivery Risk":"Medium","Overall Risk":"Medium","Backup Plan":"Yes Multiple Options"}]</table><br><table><caption>Training Program Effectiveness</caption>[{"Training Program":"Leadership Development","Participants":"45","Completion Rate":"89%","Pre-Test Score":"65%","Post-Test Score":"85%","Improvement":"+20%","Satisfaction":"4.3/5","ROI":"285%"},{"Training Program":"Technical Skills","Participants":"120","Completion Rate":"92%","Pre-Test Score":"58%","Post-Test Score":"82%","Improvement":"+24%","Satisfaction":"4.1/5","ROI":"320%"},{"Training Program":"Safety Training","Participants":"200","Completion Rate":"98%","Pre-Test Score":"72%","Post-Test Score":"94%","Improvement":"+22%","Satisfaction":"4.0/5","ROI":"450%"},{"Training Program":"Customer Service","Participants":"85","Completion Rate":"85%","Pre-Test Score":"68%","Post-Test Score":"88%","Improvement":"+20%","Satisfaction":"4.2/5","ROI":"275%"},{"Training Program":"Digital Literacy","Participants":"150","Completion Rate":"78%","Pre-Test Score":"45%","Post-Test Score":"75%","Improvement":"+30%","Satisfaction":"3.9/5","ROI":"380%"}]</table><br><table><caption>Market Research Competitive Analysis</caption>[{"Competitor":"TechGiant Corp","Market Share":"35%","Revenue Growth":"+12%","Product Quality":"High","Customer Loyalty":"Very High","Innovation Index":"9/10","Threat Level":"No Critical","Strategic Response":"Differentiation"},{"Competitor":"InnovateTech","Market Share":"18%","Revenue Growth":"+25%","Product Quality":"Medium","Customer Loyalty":"High","Innovation Index":"8/10","Threat Level":"⚠️ High","Strategic Response":"Price Competition"},{"Competitor":"StartupDisruptor","Market Share":"8%","Revenue Growth":"+45%","Product Quality":"Medium","Customer Loyalty":"Medium","Innovation Index":"7/10","Threat Level":"Yes Medium","Strategic Response":"Monitor Closely"},{"Competitor":"LegacySystems","Market Share":"22%","Revenue Growth":"+3%","Product Quality":"High","Customer Loyalty":"High","Innovation Index":"4/10","Threat Level":"Yes Low","Strategic Response":"Maintain Position"},{"Competitor":"GlobalTech Solutions","Market Share":"12%","Revenue Growth":"+8%","Product Quality":"Medium","Customer Loyalty":"Medium","Innovation Index":"6/10","Threat Level":"Yes Medium","Strategic Response":"Feature Parity"}]</table><br>', 'language': 'en'}

=== TEST\TRIAL10.TXT ===
{'content': '<table><caption>Healthcare System Performance Dashboard</caption><title>Hospital</title>[{"Hospital":"General Hospital","Bed Occupancy":"85%","Avg Length of Stay":"4.2 days","Readmission Rate":"8.5%","Revenue per Patient":"$12,500","Cost per Patient":"$9,800","Quality Rating":"Yes A+"},{"Hospital":"Children\'s Medical Center","Bed Occupancy":"78%","Avg Length of Stay":"3.1 days","Readmission Rate":"6.2%","Revenue per Patient":"$15,200","Cost per Patient":"$11,900","Quality Rating":"Yes A+"},{"Hospital":"Cardiac Specialty","Bed Occupancy":"92%","Avg Length of Stay":"5.8 days","Readmission Rate":"12.1%","Revenue per Patient":"$28,400","Cost per Patient":"$22,100","Quality Rating":"Yes A"},{"Hospital":"Emergency Care Center","Bed Occupancy":"95%","Avg Length of Stay":"0.8 days","Readmission Rate":"15.3%","Revenue per Patient":"$3,200","Cost per Patient":"$2,900","Quality Rating":"Yes B+"},{"Hospital":"Rehabilitation Institute","Bed Occupancy":"88%","Avg Length of Stay":"12.5 days","Readmission Rate":"5.8%","Revenue per Patient":"$8,900","Cost per Patient":"$7,200","Quality Rating":"Yes A"}]</table><br><table><caption>Cybersecurity Threat Intelligence Report</caption>[{"Threat Type":"Malware","Incidents Detected":"1,250","Blocked":"1,180","Investigated":"70","Resolved":"65","Avg Response Time":"15 minutes","Severity Level":"⚠️ Medium","Trend":"📈 Increasing"},{"Threat Type":"Phishing","Incidents Detected":"2,800","Blocked":"2,650","Investigated":"150","Resolved":"142","Avg Response Time":"8 minutes","Severity Level":"Yes Low","Trend":"📊 Stable"},{"Threat Type":"DDoS Attacks","Incidents Detected":"45","Blocked":"38","Investigated":"7","Resolved":"7","Avg Response Time":"45 minutes","Severity Level":"No High","Trend":"📉 Decreasing"},{"Threat Type":"Insider Threats","Incidents Detected":"12","Blocked":"0","Investigated":"12","Resolved":"10","Avg Response Time":"4 hours","Severity Level":"No Critical","Trend":"📈 Increasing"},{"Threat Type":"Ransomware","Incidents Detected":"8","Blocked":"6","Investigated":"2","Resolved":"2","Avg Response Time":"2 hours","Severity Level":"No Critical","Trend":"📊 Stable"},{"Threat Type":"Data Breach Attempts","Incidents Detected":"35","Blocked":"32","Investigated":"3","Resolved":"3","Avg Response Time":"1.5 hours","Severity Level":"No High","Trend":"📉 Decreasing"}]</table><br><table><caption>Educational Institution Performance Analysis</caption>{"Engineering College":{"Computer Science":{"Enrollment":"450","Graduation Rate":"92%","Employment Rate":"96%","Student Satisfaction":"4.5/5","Accreditation Status":"Yes Fully Accredited"},"Mechanical Engineering":{"Enrollment":"380","Graduation Rate":"88%","Employment Rate":"94%","Student Satisfaction":"4.3/5","Accreditation Status":"Yes Fully Accredited"},"Electrical Engineering":{"Enrollment":"320","Graduation Rate":"90%","Employment Rate":"95%","Student Satisfaction":"4.4/5","Accreditation Status":"Yes Fully Accredited"}},"Business School":{"MBA Program":{"Enrollment":"280","Graduation Rate":"95%","Employment Rate":"98%","Student Satisfaction":"4.6/5","Accreditation Status":"Yes Premium Accredited"},"Undergraduate Business":{"Enrollment":"520","Graduation Rate":"85%","Employment Rate":"89%","Student Satisfaction":"4.1/5","Accreditation Status":"Yes Accredited"}},"Medical School":{"MD Program":{"Enrollment":"180","Graduation Rate":"98%","Employment Rate":"100%","Student Satisfaction":"4.7/5","Accreditation Status":"Yes Premium Accredited"},"Nursing Program":{"Enrollment":"240","Graduation Rate":"94%","Employment Rate":"97%","Student Satisfaction":"4.4/5","Accreditation Status":"Yes Fully Accredited"}}}</table><br><table><caption>Renewable Energy Production Monitoring</caption>[{"Energy Source":"Solar Farm A","Capacity (MW)":"50","Current Output":"42 MW","Efficiency %":"84%","Maintenance Status":"Yes Good","Weather Impact":"Sunny","Grid Contribution":"15%","Status":"Yes Optimal"},{"Energy Source":"Solar Farm B","Capacity (MW)":"75","Current Output":"58 MW","Efficiency %":"77%","Maintenance Status":"⚠️ Scheduled","Weather Impact":"Partly Cloudy","Grid Contribution":"21%","Status":"Yes Good"},{"Energy Source":"Wind Farm Alpha","Capacity (MW)":"100","Current Output":"85 MW","Efficiency %":"85%","Maintenance Status":"Yes Excellent","Weather Impact":"High Wind","Grid Contribution":"30%","Status":"Yes Excellent"},{"Energy Source":"Wind Farm Beta","Capacity (MW)":"80","Current Output":"45 MW","Efficiency %":"56%","Maintenance Status":"Yes Good","Weather Impact":"Low Wind","Grid Contribution":"16%","Status":"Yes Suboptimal"},{"Energy Source":"Hydroelectric","Capacity (MW)":"120","Current Output":"95 MW","Efficiency %":"79%","Maintenance Status":"Yes Good","Weather Impact":"Normal Flow","Grid Contribution":"34%","Status":"Yes Good"},{"Energy Source":"Geothermal","Capacity (MW)":"30","Current Output":"28 MW","Efficiency %":"93%","Maintenance Status":"Yes Excellent","Weather Impact":"N/A","Grid Contribution":"10%","Status":"Yes Excellent"}]</table><br><table><caption>Pharmaceutical Clinical Trial Progress</caption>[{"Trial ID":"CT-2024-001","Drug Name":"CardioMax","Phase":"Phase III","Participants":"2,500","Primary Endpoint":"Reduced Heart Events","Success Rate":"78%","Side Effects":"Mild","Status":"Yes On Track","Completion Date":"Dec 2024"},{"Trial ID":"CT-2024-002","Drug Name":"NeuroHeal","Phase":"Phase II","Participants":"800","Primary Endpoint":"Cognitive Improvement","Success Rate":"65%","Side Effects":"Moderate","Status":"Yes Progressing","Completion Date":"Jun 2025"},{"Trial ID":"CT-2024-003","Drug Name":"ImmunoBoost","Phase":"Phase I","Participants":"120","Primary Endpoint":"Safety Profile","Success Rate":"92%","Side Effects":"Minimal","Status":"Yes Excellent","Completion Date":"Mar 2024"},{"Trial ID":"CT-2024-004","Drug Name":"CancerBlock","Phase":"Phase III","Participants":"1,800","Primary Endpoint":"Tumor Reduction","Success Rate":"45%","Side Effects":"Severe","Status":"⚠️ Under Review","Completion Date":"TBD"},{"Trial ID":"CT-2024-005","Drug Name":"DiabetesControl","Phase":"Phase II","Participants":"600","Primary Endpoint":"Blood Sugar Control","Success Rate":"82%","Side Effects":"Mild","Status":"Yes Promising","Completion Date":"Aug 2024"}]</table><br><table><caption>Smart City Infrastructure Monitoring</caption>{"Transportation":{"Smart Traffic Lights":{"Operational Status":"Yes 98% Operational","Utilization Rate":"85%","Maintenance Score":"A","Citizen Satisfaction":"4.2/5","Investment Priority":"Medium"},"Public Transit System":{"Operational Status":"Yes 95% Operational","Utilization Rate":"78%","Maintenance Score":"B+","Citizen Satisfaction":"3.8/5","Investment Priority":"High"},"Bike Sharing Network":{"Operational Status":"Yes 92% Operational","Utilization Rate":"65%","Maintenance Score":"B","Citizen Satisfaction":"4.0/5","Investment Priority":"Low"}},"Utilities":{"Smart Water Grid":{"Operational Status":"Yes 99% Operational","Utilization Rate":"72%","Maintenance Score":"A+","Citizen Satisfaction":"4.5/5","Investment Priority":"Low"},"Smart Energy Grid":{"Operational Status":"Yes 97% Operational","Utilization Rate":"88%","Maintenance Score":"A","Citizen Satisfaction":"4.1/5","Investment Priority":"Medium"}},"Public Safety":{"Emergency Response":{"Operational Status":"Yes 100% Operational","Utilization Rate":"45%","Maintenance Score":"A+","Citizen Satisfaction":"4.7/5","Investment Priority":"Critical"},"Smart Surveillance":{"Operational Status":"⚠️ 89% Operational","Utilization Rate":"92%","Maintenance Score":"B","Citizen Satisfaction":"3.5/5","Investment Priority":"High"}}}</table><br><table><caption>Agricultural Crop Yield Analysis</caption>[{"Crop Type":"Wheat","Planted Area (Hectares)":"1,200","Expected Yield":"4,800 tons","Actual Yield":"5,100 tons","Yield Efficiency":"Yes 106%","Weather Impact":"Favorable","Pest/Disease":"Yes Minimal","Market Price":"$280/ton"},{"Crop Type":"Corn","Planted Area (Hectares)":"800","Expected Yield":"6,400 tons","Actual Yield":"5,900 tons","Yield Efficiency":"Yes 92%","Weather Impact":"Drought Stress","Pest/Disease":"Yes Moderate","Market Price":"$220/ton"},{"Crop Type":"Soybeans","Planted Area (Hectares)":"600","Expected Yield":"1,800 tons","Actual Yield":"1,950 tons","Yield Efficiency":"Yes 108%","Weather Impact":"Optimal","Pest/Disease":"Yes None","Market Price":"$450/ton"},{"Crop Type":"Rice","Planted Area (Hectares)":"400","Expected Yield":"2,000 tons","Actual Yield":"1,750 tons","Yield Efficiency":"⚠️ 88%","Weather Impact":"Flooding","Pest/Disease":"No Significant","Market Price":"$380/ton"},{"Crop Type":"Barley","Planted Area (Hectares)":"300","Expected Yield":"1,200 tons","Actual Yield":"1,280 tons","Yield Efficiency":"Yes 107%","Weather Impact":"Good","Pest/Disease":"Yes Low","Market Price":"$200/ton"}]</table><br><table><caption>E-commerce Platform Analytics</caption>[{"Metric Category":"Total Orders","Current Month":"45,200","Previous Month":"42,800","% Change":"****%","Target":"48,000","Performance vs Target":"Yes 94%","Trend":"📈 Growing","Action Required":"Marketing Push"},{"Metric Category":"Revenue","Current Month":"$2.8M","Previous Month":"$2.6M","% Change":"****%","Target":"$3.0M","Performance vs Target":"Yes 93%","Trend":"📈 Strong","Action Required":"Upselling Focus"},{"Metric Category":"New Customers","Current Month":"8,500","Previous Month":"7,200","% Change":"+18.1%","Target":"9,000","Performance vs Target":"Yes 94%","Trend":"📈 Excellent","Action Required":"Retention Programs"},{"Metric Category":"Cart Abandonment","Current Month":"68%","Previous Month":"72%","% Change":"-4%","Target":"60%","Performance vs Target":"⚠️ 113%","Trend":"📉 Improving","Action Required":"Checkout Optimization"},{"Metric Category":"Return Rate","Current Month":"8.2%","Previous Month":"9.1%","% Change":"-9.9%","Target":"7%","Performance vs Target":"⚠️ 117%","Trend":"📉 Better","Action Required":"Quality Control"},{"Metric Category":"Customer Satisfaction","Current Month":"4.3/5","Previous Month":"4.1/5","% Change":"****%","Target":"4.5/5","Performance vs Target":"Yes 96%","Trend":"📈 Positive","Action Required":"Service Training"}]</table><br><table><caption>Space Mission Control Dashboard</caption>[{"Mission":"ISS Expedition 70","Launch Date":"2023-09-15","Current Phase":"Operational","Systems Status":"Yes All Nominal","Crew Health":"Yes Excellent","Mission Progress":"85%","Communication":"Yes Strong","Risk Level":"Low"},{"Mission":"Artemis III Prep","Launch Date":"2024-11-15","Current Phase":"Pre-Launch","Systems Status":"Yes Final Checks","Crew Health":"Yes Ready","Mission Progress":"95%","Communication":"Yes Good","Risk Level":"Medium"},{"Mission":"Mars Sample Return","Launch Date":"2024-07-20","Current Phase":"Transit","Systems Status":"Yes All Systems Go","Crew Health":"N/A (Robotic)","Mission Progress":"45%","Communication":"Yes Excellent","Risk Level":"Low"},{"Mission":"Europa Clipper","Launch Date":"2024-10-10","Current Phase":"Transit","Systems Status":"⚠️ Minor Issues","Crew Health":"N/A (Robotic)","Mission Progress":"30%","Communication":"Yes Intermittent","Risk Level":"Medium"},{"Mission":"James Webb Telescope","Launch Date":"2021-12-25","Current Phase":"Science Operations","Systems Status":"Yes Perfect","Crew Health":"N/A (Robotic)","Mission Progress":"Ongoing","Communication":"Yes Excellent","Risk Level":"Very Low"}]</table><br><table><caption>Disaster Response Coordination Matrix</caption>[{"Disaster Type":"Hurricane Alpha","Alert Level":"No Level 4","Affected Population":"250,000","Response Teams Deployed":"45 teams","Resources Allocated":"$15M","Evacuation Status":"Yes Complete","Recovery Progress":"25%","Coordination Status":"Yes Excellent"},{"Disaster Type":"Wildfire Beta","Alert Level":"⚠️ Level 3","Affected Population":"15,000","Response Teams Deployed":"28 teams","Resources Allocated":"$8M","Evacuation Status":"Yes Partial","Recovery Progress":"60%","Coordination Status":"Yes Good"},{"Disaster Type":"Earthquake Gamma","Alert Level":"Yes Level 2","Affected Population":"80,000","Response Teams Deployed":"35 teams","Resources Allocated":"$12M","Evacuation Status":"Yes Voluntary","Recovery Progress":"40%","Coordination Status":"Yes Strong"},{"Disaster Type":"Flood Delta","Alert Level":"Yes Level 1","Affected Population":"5,000","Response Teams Deployed":"12 teams","Resources Allocated":"$2M","Evacuation Status":"Yes Complete","Recovery Progress":"85%","Coordination Status":"Yes Excellent"},{"Disaster Type":"Tornado Epsilon","Alert Level":"Yes Level 2","Affected Population":"12,000","Response Teams Deployed":"18 teams","Resources Allocated":"$4M","Evacuation Status":"Yes Complete","Recovery Progress":"70%","Coordination Status":"Yes Good"}]</table><br><table><caption>Artificial Intelligence Model Performance</caption>[{"AI Model":"VisionNet v3.2","Use Case":"Image Recognition","Accuracy Rate":"96.8%","Processing Speed":"45ms","Resource Usage":"High","Training Data Size":"50M images","Last Updated":"2024-01-15","Performance Grade":"Yes A+"},{"AI Model":"LanguageBot Pro","Use Case":"Natural Language","Accuracy Rate":"94.2%","Processing Speed":"120ms","Resource Usage":"Very High","Training Data Size":"100B tokens","Last Updated":"2024-01-10","Performance Grade":"Yes A"},{"AI Model":"PredictiveAnalyzer","Use Case":"Forecasting","Accuracy Rate":"89.5%","Processing Speed":"200ms","Resource Usage":"Medium","Training Data Size":"10M records","Last Updated":"2023-12-20","Performance Grade":"Yes B+"},{"AI Model":"FraudDetector AI","Use Case":"Security","Accuracy Rate":"98.1%","Processing Speed":"15ms","Resource Usage":"Low","Training Data Size":"25M transactions","Last Updated":"2024-01-20","Performance Grade":"Yes A+"},{"AI Model":"RecommendEngine","Use Case":"Personalization","Accuracy Rate":"87.3%","Processing Speed":"80ms","Resource Usage":"Medium","Training Data Size":"500M interactions","Last Updated":"2024-01-05","Performance Grade":"Yes B"}]</table><br>', 'language': 'en'}

=== TEST\TRIAL11.TXT ===
{'content': '<table><caption>Employee Data with Missing Information</caption>[{"Employee ID":"E001","Name":"John Smith","Department":"Engineering","Phone":"","Email":"<EMAIL>","Manager":"Sarah Johnson"},{"Employee ID":"E002","Name":"Mary Davis","Department":"Marketing","Phone":"555-0123","Email":"","Manager":"Mike Wilson"},{"Employee ID":"E003","Name":"Bob Johnson","Department":"Sales","Phone":"","Email":"","Manager":"Lisa Brown"},{"Employee ID":"E004","Name":"Alice Wilson","Department":"HR","Phone":"555-0456","Email":"<EMAIL>","Manager":""},{"Employee ID":"E005","Name":"Tom Brown","Department":"Finance","Phone":"","Email":"<EMAIL>","Manager":"David Lee"}]</table><br><table><caption>Product Inventory with Incomplete Data</caption>[{"Product ID":"P001","Product Name":"Laptop Computer","Category":"Electronics","Price":"$999.99","Stock":"25","Supplier":"","Notes":"Popular item"},{"Product ID":"P002","Product Name":"Office Chair","Category":"Furniture","Price":"","Stock":"12","Supplier":"FurniCorp","Notes":""},{"Product ID":"P003","Product Name":"Wireless Mouse","Category":"Electronics","Price":"$29.99","Stock":"","Supplier":"TechSupply","Notes":"Out of stock"},{"Product ID":"P004","Product Name":"Desk Lamp","Category":"Furniture","Price":"$45.00","Stock":"8","Supplier":"","Notes":""},{"Product ID":"P005","Product Name":"Keyboard","Category":"Electronics","Price":"","Stock":"","Supplier":"TechSupply","Notes":"Discontinued"},{"Product ID":"P006","Product Name":"Monitor Stand","Category":"Accessories","Price":"$35.50","Stock":"15","Supplier":"OfficeMax","Notes":""}]</table><br><table><caption>Project Status Report with Missing Updates</caption>[{"Project Code":"PRJ001","Project Name":"Website Redesign","Team Lead":"Alice Cooper","Start Date":"2024-01-15","End Date":"2024-03-15","Status":"Complete","Budget":"$50,000","Comments":""},{"Project Code":"PRJ002","Project Name":"Mobile App Development","Team Lead":"Bob Smith","Start Date":"2024-02-01","End Date":"","Status":"In Progress","Budget":"","Comments":"Delayed due to resources"},{"Project Code":"PRJ003","Project Name":"Database Migration","Team Lead":"Carol Davis","Start Date":"","End Date":"2024-06-30","Status":"Planning","Budget":"$75,000","Comments":""},{"Project Code":"PRJ004","Project Name":"Security Audit","Team Lead":"Dan Miller","Start Date":"2024-03-01","End Date":"2024-04-15","Status":"","Budget":"$25,000","Comments":"Waiting for approval"},{"Project Code":"PRJ005","Project Name":"Cloud Migration","Team Lead":"","Start Date":"2024-04-01","End Date":"2024-08-31","Status":"Not Started","Budget":"","Comments":"Team assignment pending"},{"Project Code":"PRJ006","Project Name":"Training Program","Team Lead":"Eva Martinez","Start Date":"","End Date":"","Status":"On Hold","Budget":"$15,000","Comments":""}]</table><br><table><caption>Customer Survey Results with Partial Responses</caption>[{"Customer ID":"C001","Age Group":"25-34","Product Rating":"5","Service Rating":"","Recommendation":"Yes","Comments":"Great product quality"},{"Customer ID":"C002","Age Group":"35-44","Product Rating":"","Service Rating":"4","Recommendation":"Yes","Comments":""},{"Customer ID":"C003","Age Group":"18-24","Product Rating":"3","Service Rating":"2","Recommendation":"","Comments":"Service needs improvement"},{"Customer ID":"C004","Age Group":"","Product Rating":"4","Service Rating":"5","Recommendation":"Yes","Comments":"Excellent support team"},{"Customer ID":"C005","Age Group":"45-54","Product Rating":"","Service Rating":"","Recommendation":"No","Comments":"Product did not meet expectations"}]</table><br><table><caption>Sales Data with Missing Quarterly Information</caption>[{"Region":"North America","Q1 Sales":"$125,000","Q2 Sales":"","Q3 Sales":"$145,000","Q4 Sales":"$155,000","Total":"$425,000","Target":"$500,000","Performance":"85%"},{"Region":"Europe","Q1 Sales":"$95,000","Q2 Sales":"$105,000","Q3 Sales":"","Q4 Sales":"$115,000","Total":"$315,000","Target":"","Performance":"Unknown"},{"Region":"Asia Pacific","Q1 Sales":"","Q2 Sales":"$85,000","Q3 Sales":"$92,000","Q4 Sales":"","Total":"$177,000","Target":"$300,000","Performance":"59%"},{"Region":"Latin America","Q1 Sales":"$45,000","Q2 Sales":"$52,000","Q3 Sales":"$48,000","Q4 Sales":"$55,000","Total":"","Target":"$200,000","Performance":"100%"}]</table><br><table><caption>Equipment Maintenance Log with Incomplete Records</caption>[{"Equipment ID":"EQ001","Equipment Name":"Generator A","Last Service":"2024-01-15","Next Service":"","Technician":"Mike Johnson","Status":"Operational","Cost":"$250","Notes":""},{"Equipment ID":"EQ002","Equipment Name":"Air Compressor","Last Service":"","Next Service":"2024-04-01","Technician":"Sarah Davis","Status":"Needs Service","Cost":"","Notes":"Unusual noise reported"},{"Equipment ID":"EQ003","Equipment Name":"Forklift B","Last Service":"2024-02-20","Next Service":"2024-05-20","Technician":"","Status":"Operational","Cost":"$180","Notes":"Battery replaced"},{"Equipment ID":"EQ004","Equipment Name":"Conveyor Belt","Last Service":"2024-01-30","Next Service":"2024-04-30","Technician":"Tom Wilson","Status":"","Cost":"$320","Notes":""},{"Equipment ID":"EQ005","Equipment Name":"Packaging Machine","Last Service":"","Next Service":"","Technician":"Lisa Brown","Status":"Out of Service","Cost":"","Notes":"Awaiting parts"}]</table><br><table><caption>Training Schedule with Availability Gaps</caption>[{"Course ID":"TR001","Course Name":"Safety Training","Instructor":"John Smith","Date":"2024-03-15","Time":"","Room":"Room A","Capacity":"20","Enrolled":"18","Status":"Confirmed"},{"Course ID":"TR002","Course Name":"Leadership Skills","Instructor":"","Date":"2024-03-22","Time":"2:00 PM","Room":"Room B","Capacity":"15","Enrolled":"","Status":"Cancelled"},{"Course ID":"TR003","Course Name":"Technical Writing","Instructor":"Mary Davis","Date":"","Time":"10:00 AM","Room":"","Capacity":"12","Enrolled":"8","Status":"Scheduled"},{"Course ID":"TR004","Course Name":"Project Management","Instructor":"Bob Johnson","Date":"2024-04-05","Time":"9:00 AM","Room":"Room C","Capacity":"","Enrolled":"25","Status":"Overbooked"},{"Course ID":"TR005","Course Name":"Communication Skills","Instructor":"Alice Wilson","Date":"2024-04-12","Time":"","Room":"","Capacity":"10","Enrolled":"7","Status":"Confirmed"}]</table><br><table><caption>Budget Allocation with Pending Approvals</caption>[{"Department":"Marketing","Requested":"$100,000","Approved":"$85,000","Spent":"$45,000","Remaining":"$40,000","Pending":"","Notes":"Q2 campaign approved"},{"Department":"Engineering","Requested":"$150,000","Approved":"","Spent":"$25,000","Remaining":"","Pending":"$125,000","Notes":"Awaiting board approval"},{"Department":"Sales","Requested":"$75,000","Approved":"$75,000","Spent":"","Remaining":"$75,000","Pending":"","Notes":""},{"Department":"HR","Requested":"$50,000","Approved":"$40,000","Spent":"$35,000","Remaining":"","Pending":"$10,000","Notes":"Training budget pending"},{"Department":"Operations","Requested":"","Approved":"$120,000","Spent":"$80,000","Remaining":"$40,000","Pending":"","Notes":"Emergency fund allocated"}]</table><br><table><caption>Quality Control Test Results with Missing Data</caption>[{"Batch ID":"B001","Product":"Widget A","Test Date":"2024-03-01","Pass/Fail":"Pass","Defect Rate":"2.1%","Inspector":"","Retest Required":"No","Comments":"Within acceptable limits"},{"Batch ID":"B002","Product":"Widget B","Test Date":"","Pass/Fail":"Fail","Defect Rate":"","Inspector":"Sarah Johnson","Retest Required":"Yes","Comments":"High defect rate detected"},{"Batch ID":"B003","Product":"Widget C","Test Date":"2024-03-03","Pass/Fail":"","Defect Rate":"1.8%","Inspector":"Mike Davis","Retest Required":"","Comments":"Test in progress"},{"Batch ID":"B004","Product":"Widget D","Test Date":"2024-03-04","Pass/Fail":"Pass","Defect Rate":"0.9%","Inspector":"Lisa Brown","Retest Required":"No","Comments":""},{"Batch ID":"B005","Product":"","Test Date":"2024-03-05","Pass/Fail":"Pass","Defect Rate":"1.5%","Inspector":"","Retest Required":"No","Comments":"Product code missing"}]</table><br>', 'language': 'en'}

=== TEST\TRIAL12.TXT ===
{'content': '<table><caption>Employee Records with Missing Data Fields</caption>[{"Employee ID":"EMP001","Name":"John Smith","Department":"Engineering","Phone":"","Fax":"","Extension":"","Mobile":"","Email":"<EMAIL>","Manager":"Sarah Johnson","Notes":""},{"Employee ID":"EMP002","Name":"Mary Davis","Department":"Marketing","Phone":"555-0123","Fax":"","Extension":"","Mobile":"","Email":"","Manager":"Mike Wilson","Notes":""},{"Employee ID":"EMP003","Name":"Bob Johnson","Department":"Sales","Phone":"","Fax":"","Extension":"","Mobile":"","Email":"<EMAIL>","Manager":"Lisa Brown","Notes":""},{"Employee ID":"EMP004","Name":"Alice Wilson","Department":"HR","Phone":"555-0456","Fax":"","Extension":"","Mobile":"","Email":"<EMAIL>","Manager":"","Notes":""},{"Employee ID":"EMP005","Name":"Tom Brown","Department":"Finance","Phone":"","Fax":"","Extension":"","Mobile":"","Email":"<EMAIL>","Manager":"David Lee","Notes":""},{"Employee ID":"EMP006","Name":"Carol White","Department":"Operations","Phone":"555-0789","Fax":"","Extension":"","Mobile":"","Email":"","Manager":"Jennifer Clark","Notes":""}]</table><br><table><caption>Product Inventory with Incomplete Tracking Data</caption>[{"Product ID":"P001","Product Name":"Laptop Computer","Category":"Electronics","Price":"$999.99","Cost":"","Margin":"","Stock":"25","Reserved":"","Available":"","Supplier":"TechCorp","Notes":""},{"Product ID":"P002","Product Name":"Office Chair","Category":"Furniture","Price":"","Cost":"","Margin":"","Stock":"12","Reserved":"","Available":"","Supplier":"FurniMax","Notes":""},{"Product ID":"P003","Product Name":"Wireless Mouse","Category":"Electronics","Price":"$29.99","Cost":"","Margin":"","Stock":"","Reserved":"","Available":"","Supplier":"TechSupply","Notes":"Out of stock"},{"Product ID":"P004","Product Name":"Desk Lamp","Category":"Furniture","Price":"$45.00","Cost":"","Margin":"","Stock":"8","Reserved":"","Available":"","Supplier":"","Notes":""},{"Product ID":"P005","Product Name":"Keyboard","Category":"Electronics","Price":"","Cost":"","Margin":"","Stock":"","Reserved":"","Available":"","Supplier":"TechSupply","Notes":"Discontinued"},{"Product ID":"P006","Product Name":"Monitor Stand","Category":"Accessories","Price":"$35.50","Cost":"","Margin":"","Stock":"15","Reserved":"","Available":"","Supplier":"OfficeMax","Notes":""},{"Product ID":"P007","Product Name":"USB Cable","Category":"Electronics","Price":"$12.99","Cost":"","Margin":"","Stock":"50","Reserved":"","Available":"","Supplier":"","Notes":""}]</table><br><table><caption>Project Status Report with Incomplete Milestone Data</caption>[{"Project Code":"PRJ001","Project Name":"Website Redesign","Team Lead":"Alice Cooper","Start Date":"2024-01-15","Phase 1":"","Phase 2":"","Phase 3":"","End Date":"2024-03-15","Status":"Complete","Budget":"$50,000","Comments":""},{"Project Code":"PRJ002","Project Name":"Mobile App Development","Team Lead":"Bob Smith","Start Date":"2024-02-01","Phase 1":"","Phase 2":"","Phase 3":"","End Date":"","Status":"In Progress","Budget":"","Comments":"Delayed due to resources"},{"Project Code":"PRJ003","Project Name":"Database Migration","Team Lead":"Carol Davis","Start Date":"","Phase 1":"","Phase 2":"","Phase 3":"","End Date":"2024-06-30","Status":"Planning","Budget":"$75,000","Comments":""},{"Project Code":"PRJ004","Project Name":"Security Audit","Team Lead":"Dan Miller","Start Date":"2024-03-01","Phase 1":"","Phase 2":"","Phase 3":"","End Date":"2024-04-15","Status":"","Budget":"$25,000","Comments":"Waiting for approval"},{"Project Code":"PRJ005","Project Name":"Cloud Migration","Team Lead":"","Start Date":"2024-04-01","Phase 1":"","Phase 2":"","Phase 3":"","End Date":"2024-08-31","Status":"Not Started","Budget":"","Comments":"Team assignment pending"},{"Project Code":"PRJ006","Project Name":"Training Program","Team Lead":"Eva Martinez","Start Date":"","Phase 1":"","Phase 2":"","Phase 3":"","End Date":"","Status":"On Hold","Budget":"$15,000","Comments":""}]</table><br><table><caption>Sales Performance Data with Missing Regional Information</caption>[{"Region":"North America","Q1 Sales":"$125,000","Q1 Target":"","Q1 Variance":"","Q2 Sales":"","Q2 Target":"","Q2 Variance":"","Q3 Sales":"$145,000","Q3 Target":"$150,000","Q3 Variance":"-$5,000"},{"Region":"Europe","Q1 Sales":"$95,000","Q1 Target":"","Q1 Variance":"","Q2 Sales":"$105,000","Q2 Target":"","Q2 Variance":"","Q3 Sales":"","Q3 Target":"$120,000","Q3 Variance":""},{"Region":"Asia Pacific","Q1 Sales":"","Q1 Target":"","Q1 Variance":"","Q2 Sales":"$85,000","Q2 Target":"","Q2 Variance":"","Q3 Sales":"$92,000","Q3 Target":"$100,000","Q3 Variance":"-$8,000"},{"Region":"Latin America","Q1 Sales":"$45,000","Q1 Target":"","Q1 Variance":"","Q2 Sales":"$52,000","Q2 Target":"","Q2 Variance":"","Q3 Sales":"$48,000","Q3 Target":"$55,000","Q3 Variance":"-$7,000"},{"Region":"Middle East","Q1 Sales":"$35,000","Q1 Target":"","Q1 Variance":"","Q2 Sales":"","Q2 Target":"","Q2 Variance":"","Q3 Sales":"$42,000","Q3 Target":"$45,000","Q3 Variance":"-$3,000"},{"Region":"Africa","Q1 Sales":"","Q1 Target":"","Q1 Variance":"","Q2 Sales":"$28,000","Q2 Target":"","Q2 Variance":"","Q3 Sales":"","Q3 Target":"$35,000","Q3 Variance":""}]</table><br>', 'language': 'en'}

=== TEST\TRIAL13.TXT ===
{'content': '<table><caption>Customer Database with Missing Contact Information</caption>[{"Customer ID":"C001","Company Name":"TechCorp Solutions","Contact Person":"John Smith","Email":"<EMAIL>","Phone":"","Fax":"","Mobile":"","Address":"123 Main St","City":"New York","State":"NY","ZIP":"10001","Country":"USA","Notes":""},{"Customer ID":"C002","Company Name":"Global Industries","Contact Person":"","Email":"","Phone":"555-0123","Fax":"","Mobile":"","Address":"","City":"Los Angeles","State":"CA","ZIP":"","Country":"USA","Notes":"VIP Client"},{"Customer ID":"C003","Company Name":"Innovation Labs","Contact Person":"Sarah Johnson","Email":"<EMAIL>","Phone":"","Fax":"","Mobile":"","Address":"456 Oak Ave","City":"","State":"","ZIP":"90210","Country":"USA","Notes":""},{"Customer ID":"C004","Company Name":"Future Systems","Contact Person":"Mike Wilson","Email":"","Phone":"555-0456","Fax":"","Mobile":"","Address":"789 Pine Rd","City":"Chicago","State":"IL","ZIP":"60601","Country":"","Notes":"Potential partner"},{"Customer ID":"C005","Company Name":"Digital Dynamics","Contact Person":"","Email":"<EMAIL>","Phone":"","Fax":"","Mobile":"","Address":"","City":"Miami","State":"FL","ZIP":"33101","Country":"USA","Notes":""}]</table><table><caption>University Course Enrollment Matrix</caption><title>Fall 2023</title>[{"Course":"Computer Science 101","Enrolled":"48","Capacity":"50","Waitlist":"8"},{"Course":"Mathematics 201","Enrolled":"38","Capacity":"40","Waitlist":"3"},{"Course":"Physics 301","Enrolled":"25","Capacity":"30","Waitlist":"10"},{"Course":"Chemistry 401","Enrolled":"24","Capacity":"25","Waitlist":"6"}]</table><table><caption>Employee Performance Review Scores</caption>[{"Employee":"Alice Johnson","Q1 Score":"8.5","Q2 Score":"9.0","Q3 Score":"8.8","Q4 Score":"9.2","Average":"8.9"},{"Employee":"Bob Smith","Q1 Score":"7.8","Q2 Score":"8.2","Q3 Score":"8.0","Q4 Score":"8.5","Average":"8.1"},{"Employee":"Carol Davis","Q1 Score":"9.1","Q2 Score":"8.9","Q3 Score":"9.3","Q4 Score":"9.0","Average":"9.1"},{"Employee":"David Wilson","Q1 Score":"7.5","Q2 Score":"7.8","Q3 Score":"8.1","Q4 Score":"8.3","Average":"7.9"},{"Employee":"Eva Martinez","Q1 Score":"8.8","Q2 Score":"9.1","Q3 Score":"8.7","Q4 Score":"9.4","Average":"9.0"}]</table><table><caption>Product Catalog with Incomplete Specifications</caption>[{"SKU":"SKU001","Product Name":"Wireless Headphones","Category":"Electronics","Brand":"AudioTech","Model":"AT-WH100","Color":"Black","Size":"","Weight":"","Dimensions":"","Material":"Plastic","Warranty":"2 years","Price":"$89.99","Stock":"25"},{"SKU":"SKU002","Product Name":"Office Desk","Category":"Furniture","Brand":"WorkSpace","Model":"","Color":"Brown","Size":"Large","Weight":"","Dimensions":"60x30x29 inches","Material":"Wood","Warranty":"","Price":"$299.99","Stock":"8"},{"SKU":"SKU003","Product Name":"Running Shoes","Category":"Footwear","Brand":"SportMax","Model":"SM-RS200","Color":"","Size":"10","Weight":"","Dimensions":"","Material":"Synthetic","Warranty":"1 year","Price":"","Stock":"15"},{"SKU":"SKU004","Product Name":"Coffee Maker","Category":"Appliances","Brand":"","Model":"CM-500","Color":"Silver","Size":"","Weight":"5 lbs","Dimensions":"","Material":"Stainless Steel","Warranty":"","Price":"$149.99","Stock":""},{"SKU":"SKU005","Product Name":"Backpack","Category":"Accessories","Brand":"TravelPro","Model":"","Color":"Blue","Size":"Medium","Weight":"","Dimensions":"","Material":"","Warranty":"2 years","Price":"$79.99","Stock":"12"}]</table><table><caption>Regional Sales Performance by Quarter</caption><title>2023 Sales ($000)</title>[{"Region":"North America","Q1":"1,250","Q2":"1,380","Q3":"1,420","Q4":"1,650"},{"Region":"Europe","Q1":"980","Q2":"1,120","Q3":"1,050","Q4":"1,280"},{"Region":"Asia Pacific","Q1":"750","Q2":"890","Q3":"920","Q4":"1,100"},{"Region":"Latin America","Q1":"420","Q2":"480","Q3":"510","Q4":"580"},{"Region":"Middle East & Africa","Q1":"320","Q2":"380","Q3":"410","Q4":"450"}]</table><table><caption>IT Asset Inventory with Missing Details</caption>[{"Asset ID":"IT001","Asset Type":"Laptop","Brand":"Dell","Model":"Latitude 5520","Serial Number":"DL123456","Purchase Date":"2023-01-15","Warranty Expiry":"","Location":"Office A","Assigned To":"John Smith","Status":"Active","Last Maintenance":"","Next Maintenance":"","Notes":""},{"Asset ID":"IT002","Asset Type":"Desktop","Brand":"HP","Model":"","Serial Number":"","Purchase Date":"2022-11-20","Warranty Expiry":"2025-11-20","Location":"","Assigned To":"Mary Johnson","Status":"Active","Last Maintenance":"2024-01-10","Next Maintenance":"","Notes":"Needs RAM upgrade"},{"Asset ID":"IT003","Asset Type":"Printer","Brand":"Canon","Model":"ImageClass MF445dw","Serial Number":"CN789012","Purchase Date":"","Warranty Expiry":"","Location":"Office B","Assigned To":"","Status":"Maintenance","Last Maintenance":"","Next Maintenance":"2024-03-15","Notes":"Toner replacement needed"},{"Asset ID":"IT004","Asset Type":"Monitor","Brand":"Samsung","Model":"27\\" Curved","Serial Number":"","Purchase Date":"2023-03-10","Warranty Expiry":"2026-03-10","Location":"Office A","Assigned To":"Bob Wilson","Status":"","Last Maintenance":"","Next Maintenance":"","Notes":""},{"Asset ID":"IT005","Asset Type":"Server","Brand":"","Model":"PowerEdge R740","Serial Number":"SV345678","Purchase Date":"2022-08-05","Warranty Expiry":"","Location":"Data Center","Assigned To":"IT Team","Status":"Active","Last Maintenance":"2024-02-01","Next Maintenance":"2024-05-01","Notes":""}]</table><table><caption>Project Budget Allocation</caption><title>Project Phase</title>[{"Project Phase":"Planning","Personnel":"$25,000","Equipment":"$5,000","Other":"$3,000","Total":"$33,000"},{"Project Phase":"Development","Personnel":"$150,000","Equipment":"$30,000","Other":"$15,000","Total":"$195,000"},{"Project Phase":"Testing","Personnel":"$45,000","Equipment":"$10,000","Other":"$8,000","Total":"$63,000"},{"Project Phase":"Deployment","Personnel":"$20,000","Equipment":"$15,000","Other":"$5,000","Total":"$40,000"},{"Project Phase":"Maintenance","Personnel":"$30,000","Equipment":"$8,000","Other":"$7,000","Total":"$45,000"}]</table><table><caption>Training Schedule Matrix</caption>[{"Training Course":"Safety Protocols","Instructor":"Sarah Miller","Duration":"4 hours","Max Participants":"20","Cost per Person":"$150"},{"Training Course":"Software Development","Instructor":"Mike Chen","Duration":"2 days","Max Participants":"12","Cost per Person":"$800"},{"Training Course":"Project Management","Instructor":"Lisa Brown","Duration":"3 days","Max Participants":"15","Cost per Person":"$1,200"},{"Training Course":"Customer Service","Instructor":"Tom Wilson","Duration":"1 day","Max Participants":"25","Cost per Person":"$300"},{"Training Course":"Data Analysis","Instructor":"Emma Davis","Duration":"5 days","Max Participants":"10","Cost per Person":"$1,500"}]</table>', 'language': 'en'}

=== TEST\TRIAL14.TXT ===
{'content': '<table><caption>Financial Statement - Income and Expenses</caption>{"Revenue":{"Product Sales":{"2023":"$2,500,000","2022":"$2,200,000","Change":"+13.6%"},"Service Revenue":{"2023":"$800,000","2022":"$750,000","Change":"+6.7%"},"Licensing":{"2023":"$300,000","2022":"$280,000","Change":"+7.1%"},"Total Revenue":{"2023":"$3,600,000","2022":"$3,230,000","Change":"+11.5%"}},"Expenses":{"Cost of Goods Sold":{"2023":"$1,400,000","2022":"$1,250,000","Change":"+12.0%"},"Salaries & Benefits":{"2023":"$1,200,000","2022":"$1,100,000","Change":"+9.1%"},"Marketing":{"2023":"$350,000","2022":"$320,000","Change":"+9.4%"},"Operations":{"2023":"$280,000","2022":"$260,000","Change":"****%"},"Total Expenses":{"2023":"$3,230,000","2022":"$2,930,000","Change":"+10.2%"}},"Net Income":{"Net Income":{"2023":"$370,000","2022":"$300,000","Change":"+23.3%"}}}</table><table><caption>Manufacturing Equipment Status with Missing Data</caption>[{"Equipment ID":"EQ001","Equipment Name":"CNC Machine","Location":"Floor A","Manufacturer":"Haas","Model":"VF-2","Year":"2020","Status":"Operational","Last Service":"","Next Service":"","Operator":"Mike Johnson","Shift":"Day","Efficiency":"95%","Downtime Hours":""},{"Equipment ID":"EQ002","Equipment Name":"Injection Molding","Location":"Floor B","Manufacturer":"","Model":"IM-500","Year":"2019","Status":"","Last Service":"2024-01-15","Next Service":"","Operator":"","Shift":"Night","Efficiency":"","Downtime Hours":"8"},{"Equipment ID":"EQ003","Equipment Name":"Assembly Line","Location":"Floor C","Manufacturer":"Bosch","Model":"","Year":"","Status":"Operational","Last Service":"","Next Service":"2024-04-01","Operator":"Sarah Davis","Shift":"","Efficiency":"88%","Downtime Hours":""},{"Equipment ID":"EQ004","Equipment Name":"Quality Scanner","Location":"","Manufacturer":"Keyence","Model":"CV-X100","Year":"2021","Status":"Maintenance","Last Service":"2024-02-10","Next Service":"","Operator":"","Shift":"Day","Efficiency":"","Downtime Hours":"12"},{"Equipment ID":"EQ005","Equipment Name":"Packaging Unit","Location":"Floor D","Manufacturer":"","Model":"PU-200","Year":"2018","Status":"Operational","Last Service":"","Next Service":"2024-03-20","Operator":"Tom Wilson","Shift":"Day","Efficiency":"92%","Downtime Hours":""},{"Equipment ID":"EQ006","Equipment Name":"Conveyor System","Location":"Floor A-D","Manufacturer":"Siemens","Model":"CS-1000","Year":"","Status":"","Last Service":"","Next Service":"","Operator":"","Shift":"Evening","Efficiency":"98%","Downtime Hours":"2"}]</table><table><caption>Market Research Survey Results</caption><title>Age Groups</title>[{"Product Feature":"Price Sensitivity","18-25":"High","26-35":"Medium","36-45":"Medium","46-55":"Low","56+":"Low"},{"Product Feature":"Brand Loyalty","18-25":"Low","26-35":"Medium","36-45":"High","46-55":"High","56+":"Very High"},{"Product Feature":"Tech Features","18-25":"Very Important","26-35":"Important","36-45":"Moderate","46-55":"Low","56+":"Very Low"},{"Product Feature":"Customer Support","18-25":"Moderate","26-35":"Important","36-45":"Very Important","46-55":"Very Important","56+":"Critical"},{"Product Feature":"Sustainability","18-25":"Very Important","26-35":"Important","36-45":"Important","46-55":"Moderate","56+":"Moderate"}]</table><table><caption>Supplier Performance Scorecard</caption>[{"Supplier":"ABC Manufacturing","Quality Score":"9.2","Delivery Score":"8.8","Cost Score":"7.5","Service Score":"9.0","Overall Rating":"8.6"},{"Supplier":"XYZ Components","Quality Score":"8.5","Delivery Score":"9.1","Cost Score":"8.2","Service Score":"8.7","Overall Rating":"8.6"},{"Supplier":"Global Supplies Inc","Quality Score":"7.8","Delivery Score":"8.0","Cost Score":"9.3","Service Score":"7.9","Overall Rating":"8.3"},{"Supplier":"TechParts Ltd","Quality Score":"9.0","Delivery Score":"7.5","Cost Score":"7.8","Service Score":"8.5","Overall Rating":"8.2"},{"Supplier":"Premium Materials","Quality Score":"9.5","Delivery Score":"8.9","Cost Score":"6.8","Service Score":"9.2","Overall Rating":"8.6"}]</table><table><caption>Research Lab Equipment Inventory with Gaps</caption>[{"Lab ID":"LAB001","Equipment Type":"Microscope","Brand":"Zeiss","Model":"Axio Observer","Serial":"ZS123456","Calibration Date":"2024-01-10","Next Calibration":"","Condition":"Excellent","Responsible Person":"Dr. Smith","Usage Hours":"","Maintenance Cost":"","Replacement Value":"$25,000","Notes":""},{"Lab ID":"LAB002","Equipment Type":"Centrifuge","Brand":"","Model":"CF-1000","Serial":"","Calibration Date":"","Next Calibration":"2024-06-15","Condition":"Good","Responsible Person":"","Usage Hours":"1,250","Maintenance Cost":"$500","Replacement Value":"","Notes":"Needs new rotor"},{"Lab ID":"LAB003","Equipment Type":"Spectrophotometer","Brand":"Thermo Fisher","Model":"","Serial":"TF789012","Calibration Date":"2023-12-05","Next Calibration":"2024-12-05","Condition":"","Responsible Person":"Dr. Johnson","Usage Hours":"","Maintenance Cost":"","Replacement Value":"$18,000","Notes":""},{"Lab ID":"LAB004","Equipment Type":"Incubator","Brand":"Binder","Model":"BD-240","Serial":"","Calibration Date":"","Next Calibration":"","Condition":"Fair","Responsible Person":"Lab Tech A","Usage Hours":"8,760","Maintenance Cost":"","Replacement Value":"$8,500","Notes":"Temperature fluctuation"},{"Lab ID":"LAB005","Equipment Type":"Balance","Brand":"Mettler Toledo","Model":"XS205","Serial":"MT345678","Calibration Date":"2024-02-20","Next Calibration":"2025-02-20","Condition":"Excellent","Responsible Person":"","Usage Hours":"","Maintenance Cost":"$150","Replacement Value":"","Notes":""}]</table><table><caption>Website Analytics Dashboard</caption><title>January 2024</title>[{"Metric":"Page Views","Week 1":"15,300","Week 2":"16,800","Week 3":"17,500"},{"Metric":"Unique Visitors","Week 1":"10,100","Week 2":"11,200","Week 3":"11,800"},{"Metric":"Bounce Rate (%)","Week 1":"30.5","Week 2":"29.8","Week 3":"28.9"},{"Metric":"Avg Session Duration","Week 1":"3:15","Week 2":"3:22","Week 3":"3:35"},{"Metric":"Conversion Rate (%)","Week 1":"3.7","Week 2":"4.0","Week 3":"4.2"}]</table><table><caption>Employee Skills Assessment Matrix</caption>[{"Employee":"Alice Cooper","Technical Skills":"Expert","Communication":"Advanced","Leadership":"Intermediate","Problem Solving":"Expert","Teamwork":"Advanced","Overall Score":"4.4"},{"Employee":"Bob Martinez","Technical Skills":"Advanced","Communication":"Expert","Leadership":"Advanced","Problem Solving":"Advanced","Teamwork":"Expert","Overall Score":"4.6"},{"Employee":"Carol Thompson","Technical Skills":"Intermediate","Communication":"Advanced","Leadership":"Expert","Problem Solving":"Advanced","Teamwork":"Advanced","Overall Score":"4.2"},{"Employee":"David Lee","Technical Skills":"Expert","Communication":"Intermediate","Leadership":"Intermediate","Problem Solving":"Expert","Teamwork":"Advanced","Overall Score":"4.2"},{"Employee":"Emma Wilson","Technical Skills":"Advanced","Communication":"Advanced","Leadership":"Advanced","Problem Solving":"Advanced","Teamwork":"Expert","Overall Score":"4.4"}]</table>', 'language': 'en'}

=== TEST\TRIAL15.TXT ===
{'content': '<table><caption>Hospital Patient Records with Missing Information</caption>[{"Patient ID":"P001","Name":"John Anderson","Age":"45","Gender":"Male","Blood Type":"O+","Allergies":"","Emergency Contact":"","Insurance":"BlueCross","Primary Doctor":"Dr. Smith","Admission Date":"2024-01-15","Discharge Date":"","Room Number":"101","Status":"Admitted"},{"Patient ID":"P002","Name":"Mary Johnson","Age":"","Gender":"Female","Blood Type":"","Allergies":"Penicillin","Emergency Contact":"555-0123","Insurance":"","Primary Doctor":"Dr. Wilson","Admission Date":"","Discharge Date":"2024-01-20","Room Number":"","Status":"Discharged"},{"Patient ID":"P003","Name":"Robert Davis","Age":"67","Gender":"","Blood Type":"A-","Allergies":"","Emergency Contact":"","Insurance":"Medicare","Primary Doctor":"","Admission Date":"2024-01-18","Discharge Date":"","Room Number":"205","Status":"Admitted"},{"Patient ID":"P004","Name":"Lisa Brown","Age":"32","Gender":"Female","Blood Type":"B+","Allergies":"Latex","Emergency Contact":"","Insurance":"Aetna","Primary Doctor":"Dr. Johnson","Admission Date":"","Discharge Date":"","Room Number":"","Status":"Outpatient"},{"Patient ID":"P005","Name":"Michael Wilson","Age":"28","Gender":"Male","Blood Type":"","Allergies":"","Emergency Contact":"555-0456","Insurance":"","Primary Doctor":"Dr. Smith","Admission Date":"2024-01-22","Discharge Date":"","Room Number":"150","Status":""}]</table><table><caption>Climate Data by Region</caption><title>Temperature (°F)</title>[{"Region":"Northeast","Spring":"3.2","Summer":"4.1","Fall":"3.8","Winter":"2.9"},{"Region":"Southeast","Spring":"4.5","Summer":"6.2","Fall":"3.1","Winter":"2.8"},{"Region":"Midwest","Spring":"3.8","Summer":"4.3","Fall":"2.9","Winter":"1.8"},{"Region":"Southwest","Spring":"1.2","Summer":"2.8","Fall":"1.5","Winter":"1.1"},{"Region":"West Coast","Spring":"2.1","Summer":"0.8","Fall":"3.2","Winter":"5.1"}]</table><table><caption>Software License Management</caption>[{"Software":"Microsoft Office","Version":"365","License Type":"Subscription","Seats Purchased":"100","Seats Used":"87","Expiry Date":"2024-12-31","Annual Cost":"$8,500"},{"Software":"Adobe Creative Suite","Version":"2024","License Type":"Subscription","Seats Purchased":"25","Seats Used":"23","Expiry Date":"2024-06-30","Annual Cost":"$15,000"},{"Software":"AutoCAD","Version":"2024","License Type":"Perpetual","Seats Purchased":"10","Seats Used":"8","Expiry Date":"N/A","Annual Cost":"$20,000"},{"Software":"Salesforce","Version":"Enterprise","License Type":"Subscription","Seats Purchased":"50","Seats Used":"45","Expiry Date":"2024-09-15","Annual Cost":"$36,000"},{"Software":"Slack","Version":"Pro","License Type":"Subscription","Seats Purchased":"150","Seats Used":"142","Expiry Date":"2024-08-20","Annual Cost":"$12,000"}]</table><table><caption>Vehicle Fleet Management with Missing Records</caption>[{"Vehicle ID":"V001","Make":"Ford","Model":"Transit","Year":"2022","VIN":"1FTBW2CM6NKA12345","License Plate":"","Mileage":"25,000","Last Service":"2024-01-10","Next Service":"","Assigned Driver":"Mike Johnson","Department":"Delivery","Fuel Type":"","Status":"Active"},{"Vehicle ID":"V002","Make":"Chevrolet","Model":"","Year":"2021","VIN":"","License Plate":"ABC-123","Mileage":"","Last Service":"","Next Service":"2024-04-15","Assigned Driver":"","Department":"Sales","Fuel Type":"Gasoline","Status":""},{"Vehicle ID":"V003","Make":"Toyota","Model":"Prius","Year":"","VIN":"JTDKARFU5J3123456","License Plate":"XYZ-789","Mileage":"18,500","Last Service":"","Next Service":"","Assigned Driver":"Sarah Davis","Department":"","Fuel Type":"Hybrid","Status":"Active"},{"Vehicle ID":"V004","Make":"","Model":"Sprinter","Year":"2020","VIN":"WD3PE8CC5L5123456","License Plate":"","Mileage":"45,000","Last Service":"2024-02-05","Next Service":"2024-05-05","Assigned Driver":"","Department":"Maintenance","Fuel Type":"Diesel","Status":"Service"},{"Vehicle ID":"V005","Make":"Nissan","Model":"Leaf","Year":"2023","VIN":"","License Plate":"ECO-456","Mileage":"","Last Service":"","Next Service":"","Assigned Driver":"Emma Wilson","Department":"Executive","Fuel Type":"","Status":"Active"}]</table><table><caption>Academic Course Prerequisites</caption>[{"Course Code":"CS101","Course Name":"Introduction to Programming","Prerequisites":"None","Credits":"3","Semester Offered":"Fall, Spring"},{"Course Code":"CS201","Course Name":"Data Structures","Prerequisites":"CS101","Credits":"4","Semester Offered":"Spring, Summer"},{"Course Code":"CS301","Course Name":"Algorithms","Prerequisites":"CS201, MATH201","Credits":"4","Semester Offered":"Fall"},{"Course Code":"CS401","Course Name":"Software Engineering","Prerequisites":"CS201, CS250","Credits":"3","Semester Offered":"Spring"},{"Course Code":"CS450","Course Name":"Machine Learning","Prerequisites":"CS301, STAT301","Credits":"4","Semester Offered":"Fall, Spring"}]</table><table><caption>Laboratory Test Results with Incomplete Data</caption>[{"Sample ID":"S001","Test Type":"Blood Chemistry","Parameter":"Glucose","Result":"95","Unit":"mg/dL","Reference Range":"70-100","Status":"Normal","Test Date":"2024-01-15","Technician":"","Equipment Used":"","Batch Number":"********","Quality Control":"Pass","Notes":""},{"Sample ID":"S002","Test Type":"Microbiology","Parameter":"E. coli","Result":"","Unit":"CFU/mL","Reference Range":"","Status":"Pending","Test Date":"","Technician":"Lab Tech B","Equipment Used":"Incubator A","Batch Number":"","Quality Control":"","Notes":"48h incubation"},{"Sample ID":"S003","Test Type":"Chemistry","Parameter":"pH","Result":"7.2","Unit":"","Reference Range":"6.5-8.5","Status":"","Test Date":"2024-01-16","Technician":"Lab Tech A","Equipment Used":"","Batch Number":"********","Quality Control":"","Notes":""},{"Sample ID":"S004","Test Type":"Hematology","Parameter":"","Result":"4.8","Unit":"x10^6/μL","Reference Range":"4.5-5.5","Status":"Normal","Test Date":"2024-01-17","Technician":"","Equipment Used":"Counter XYZ","Batch Number":"","Quality Control":"Pass","Notes":""},{"Sample ID":"S005","Test Type":"Immunology","Parameter":"IgG","Result":"","Unit":"mg/dL","Reference Range":"700-1600","Status":"Retest","Test Date":"","Technician":"Lab Tech C","Equipment Used":"","Batch Number":"********","Quality Control":"","Notes":"Dilution error"}]</table><table><caption>Event Planning Checklist</caption>[{"Task Category":"Venue","Task":"Book conference hall","Responsible Person":"Sarah Johnson","Due Date":"2024-02-01","Status":"Complete","Priority":"High"},{"Task Category":"Catering","Task":"Arrange lunch menu","Responsible Person":"Mike Wilson","Due Date":"2024-02-15","Status":"In Progress","Priority":"High"},{"Task Category":"Technology","Task":"Setup AV equipment","Responsible Person":"Tom Davis","Due Date":"2024-03-01","Status":"Not Started","Priority":"Medium"},{"Task Category":"Marketing","Task":"Send invitations","Responsible Person":"Lisa Brown","Due Date":"2024-02-10","Status":"Complete","Priority":"High"},{"Task Category":"Registration","Task":"Setup online registration","Responsible Person":"Emma Martinez","Due Date":"2024-02-05","Status":"In Progress","Priority":"Medium"}]</table>', 'language': 'en'}

=== TEST\TRIAL16.TXT ===
{'content': '<table><caption>Quarterly Business Review</caption><title>Q1 2024</title>[{"Department":"Sales","Revenue":"$920K","Expenses":"$340K","Profit":"$580K","Growth":"+8%"},{"Department":"Marketing","Revenue":"$150K","Expenses":"$300K","Profit":"-$150K","Growth":"+25%"},{"Department":"Operations","Revenue":"$220K","Expenses":"$190K","Profit":"$30K","Growth":"+10%"},{"Department":"R&D","Revenue":"$80K","Expenses":"$420K","Profit":"-$340K","Growth":"+60%"}]</table><table><caption>Retail Store Inventory with Stock Gaps</caption>[{"Product Code":"RC001","Product Name":"Cotton T-Shirt","Category":"Apparel","Brand":"BasicWear","Size":"M","Color":"Blue","Current Stock":"15","Reorder Level":"","Max Stock":"","Supplier":"TextileCorp","Unit Cost":"$8.50","Retail Price":"","Last Restock":"2024-01-10"},{"Product Code":"RC002","Product Name":"Denim Jeans","Category":"Apparel","Brand":"","Size":"32","Color":"","Current Stock":"","Reorder Level":"10","Max Stock":"50","Supplier":"","Unit Cost":"$25.00","Retail Price":"$79.99","Last Restock":""},{"Product Code":"RC003","Product Name":"Running Shoes","Category":"Footwear","Brand":"SportMax","Size":"","Color":"Black","Current Stock":"8","Reorder Level":"5","Max Stock":"","Supplier":"ShoeSupply","Unit Cost":"","Retail Price":"$129.99","Last Restock":"2024-01-15"},{"Product Code":"RC004","Product Name":"Leather Wallet","Category":"Accessories","Brand":"LuxLeather","Size":"Standard","Color":"","Current Stock":"25","Reorder Level":"","Max Stock":"40","Supplier":"LeatherWorks","Unit Cost":"$15.00","Retail Price":"","Last Restock":""},{"Product Code":"RC005","Product Name":"Baseball Cap","Category":"","Brand":"CapCo","Size":"One Size","Color":"Red","Current Stock":"","Reorder Level":"8","Max Stock":"","Supplier":"","Unit Cost":"$6.50","Retail Price":"$24.99","Last Restock":"2024-01-20"},{"Product Code":"RC006","Product Name":"Backpack","Category":"Accessories","Brand":"","Size":"Large","Color":"Green","Current Stock":"12","Reorder Level":"6","Max Stock":"30","Supplier":"BagMakers","Unit Cost":"","Retail Price":"$89.99","Last Restock":""}]</table><table><caption>University Research Grants</caption>[{"Grant ID":"G2024-001","Principal Investigator":"Dr. Sarah Chen","Project Title":"AI in Medical Diagnosis","Funding Agency":"NIH","Amount":"$250,000","Start Date":"2024-01-01","End Date":"2026-12-31","Status":"Active"},{"Grant ID":"G2024-002","Principal Investigator":"Prof. Michael Brown","Project Title":"Renewable Energy Storage","Funding Agency":"DOE","Amount":"$180,000","Start Date":"2024-03-01","End Date":"2025-02-28","Status":"Active"},{"Grant ID":"G2024-003","Principal Investigator":"Dr. Lisa Martinez","Project Title":"Climate Change Modeling","Funding Agency":"NSF","Amount":"$320,000","Start Date":"2024-06-01","End Date":"2027-05-31","Status":"Pending"},{"Grant ID":"G2024-004","Principal Investigator":"Prof. David Wilson","Project Title":"Quantum Computing Applications","Funding Agency":"IBM","Amount":"$150,000","Start Date":"2024-02-15","End Date":"2025-02-14","Status":"Active"},{"Grant ID":"G2024-005","Principal Investigator":"Dr. Emma Johnson","Project Title":"Biodiversity Conservation","Funding Agency":"EPA","Amount":"$200,000","Start Date":"2024-04-01","End Date":"2026-03-31","Status":"Under Review"}]</table><table><caption>Sports Team Statistics</caption>[{"Player":"Mike Johnson","Position":"Point Guard","Games Played":"28","Points":"18.5","Assists":"8.2","Rebounds":"4.1","Field Goal %":"45.2%","Free Throw %":"82.1%"},{"Player":"Sarah Davis","Position":"Shooting Guard","Games Played":"30","Points":"22.8","Assists":"3.5","Rebounds":"5.2","Field Goal %":"48.7%","Free Throw %":"78.9%"},{"Player":"Tom Wilson","Position":"Small Forward","Games Played":"29","Points":"16.3","Assists":"4.8","Rebounds":"7.1","Field Goal %":"42.1%","Free Throw %":"75.3%"},{"Player":"Lisa Brown","Position":"Power Forward","Games Played":"27","Points":"14.7","Assists":"2.1","Rebounds":"9.8","Field Goal %":"51.2%","Free Throw %":"68.4%"},{"Player":"David Lee","Position":"Center","Games Played":"25","Points":"12.9","Assists":"1.8","Rebounds":"11.5","Field Goal %":"55.8%","Free Throw %":"62.7%"}]</table><table><caption>Medical Equipment Maintenance Schedule with Missing Entries</caption>[{"Equipment ID":"MED001","Equipment Name":"MRI Scanner","Department":"Radiology","Manufacturer":"Siemens","Model":"Magnetom Vida","Last Maintenance":"2024-01-15","Next Maintenance":"","Maintenance Type":"Preventive","Technician":"","Cost":"$2,500","Downtime":"","Status":"Operational","Notes":""},{"Equipment ID":"MED002","Equipment Name":"CT Scanner","Department":"Radiology","Manufacturer":"","Model":"Revolution CT","Last Maintenance":"","Next Maintenance":"2024-04-10","Maintenance Type":"","Technician":"Tech Support","Cost":"","Downtime":"4 hours","Status":"","Notes":"Calibration needed"},{"Equipment ID":"MED003","Equipment Name":"Ultrasound","Department":"Cardiology","Manufacturer":"Philips","Model":"","Last Maintenance":"2024-02-01","Next Maintenance":"2024-05-01","Maintenance Type":"Preventive","Technician":"Mike Johnson","Cost":"","Downtime":"","Status":"Operational","Notes":""},{"Equipment ID":"MED004","Equipment Name":"X-Ray Machine","Department":"","Manufacturer":"Canon","Model":"CXDI-801C","Last Maintenance":"","Next Maintenance":"","Maintenance Type":"Corrective","Technician":"","Cost":"$800","Downtime":"2 hours","Status":"Repair","Notes":"Detector issue"},{"Equipment ID":"MED005","Equipment Name":"Ventilator","Department":"ICU","Manufacturer":"Medtronic","Model":"PB980","Last Maintenance":"2024-01-20","Next Maintenance":"2024-04-20","Maintenance Type":"","Technician":"Sarah Davis","Cost":"$150","Downtime":"","Status":"","Notes":""}]</table><table><caption>Customer Satisfaction Survey Results</caption><title>Rating Distribution (%)</title>[{"Service Aspect":"Product Quality","Excellent (5)":"45","Good (4)":"35","Fair (3)":"15","Poor (2)":"4","Very Poor (1)":"1","Average Score":"4.19"},{"Service Aspect":"Customer Service","Excellent (5)":"38","Good (4)":"42","Fair (3)":"16","Poor (2)":"3","Very Poor (1)":"1","Average Score":"4.13"},{"Service Aspect":"Delivery Speed","Excellent (5)":"32","Good (4)":"38","Fair (3)":"22","Poor (2)":"6","Very Poor (1)":"2","Average Score":"3.92"},{"Service Aspect":"Value for Money","Excellent (5)":"28","Good (4)":"45","Fair (3)":"20","Poor (2)":"5","Very Poor (1)":"2","Average Score":"3.92"},{"Service Aspect":"Overall Experience","Excellent (5)":"41","Good (4)":"39","Fair (3)":"16","Poor (2)":"3","Very Poor (1)":"1","Average Score":"4.16"}]</table><table><caption>Project Timeline Milestones</caption>[{"Phase":"Planning","Milestone":"Requirements Gathering","Planned Start":"2024-01-01","Planned End":"2024-01-15","Actual Start":"2024-01-01","Actual End":"2024-01-12","Status":"Complete","Owner":"Sarah Johnson"},{"Phase":"Design","Milestone":"System Architecture","Planned Start":"2024-01-16","Planned End":"2024-02-15","Actual Start":"2024-01-13","Actual End":"2024-02-10","Status":"Complete","Owner":"Mike Chen"},{"Phase":"Development","Milestone":"Core Features","Planned Start":"2024-02-16","Planned End":"2024-04-30","Actual Start":"2024-02-11","Actual End":"TBD","Status":"In Progress","Owner":"Dev Team"},{"Phase":"Testing","Milestone":"System Testing","Planned Start":"2024-05-01","Planned End":"2024-05-31","Actual Start":"TBD","Actual End":"TBD","Status":"Not Started","Owner":"QA Team"},{"Phase":"Deployment","Milestone":"Production Release","Planned Start":"2024-06-01","Planned End":"2024-06-15","Actual Start":"TBD","Actual End":"TBD","Status":"Not Started","Owner":"DevOps Team"}]</table>', 'language': 'en'}

=== TEST\TRIAL17.TXT ===
{'content': '<table><caption>Construction Project Materials with Missing Specifications</caption>[{"Material ID":"MAT001","Material Name":"Steel Rebar","Category":"Structural","Supplier":"SteelCorp","Unit":"Tons","Quantity Ordered":"50","Quantity Received":"","Unit Price":"$800","Total Cost":"","Delivery Date":"2024-02-15","Quality Grade":"Grade 60","Storage Location":"","Notes":""},{"Material ID":"MAT002","Material Name":"Concrete Mix","Category":"Foundation","Supplier":"","Unit":"Cubic Yards","Quantity Ordered":"","Quantity Received":"200","Unit Price":"","Total Cost":"$120","Delivery Date":"","Quality Grade":"","Storage Location":"Yard A","Notes":"High strength"},{"Material ID":"MAT003","Material Name":"Lumber 2x4","Category":"Framing","Supplier":"WoodSupply","Unit":"","Quantity Ordered":"500","Quantity Received":"450","Unit Price":"$8.50","Total Cost":"","Delivery Date":"2024-02-10","Quality Grade":"","Storage Location":"Warehouse B","Notes":""},{"Material ID":"MAT004","Material Name":"Roofing Shingles","Category":"","Supplier":"RoofMaster","Unit":"Squares","Quantity Ordered":"25","Quantity Received":"","Unit Price":"","Total Cost":"$2,500","Delivery Date":"2024-03-01","Quality Grade":"Architectural","Storage Location":"","Notes":"Weather delay"},{"Material ID":"MAT005","Material Name":"Electrical Wire","Category":"Electrical","Supplier":"","Unit":"Feet","Quantity Ordered":"2000","Quantity Received":"2000","Unit Price":"$2.50","Total Cost":"$5,000","Delivery Date":"","Quality Grade":"","Storage Location":"Storage C","Notes":""},{"Material ID":"MAT006","Material Name":"Insulation","Category":"Thermal","Supplier":"InsulPro","Unit":"Rolls","Quantity Ordered":"","Quantity Received":"100","Unit Price":"","Total Cost":"","Delivery Date":"2024-02-20","Quality Grade":"R-30","Storage Location":"","Notes":"Fire resistant"}]</table><table><caption>Restaurant Menu Analysis</caption><title>Appetizers</title>[{"Metric":"Orders per Day","Popular":"30","Average":"18","Unpopular":"5"},{"Metric":"Profit Margin (%)","Popular":"72","Average":"60","Unpopular":"45"},{"Metric":"Prep Time (min)","Popular":"20","Average":"25","Unpopular":"30"},{"Metric":"Customer Rating","Popular":"4.7","Average":"4.0","Unpopular":"3.4"}]</table><table><caption>Library Book Collection Statistics</caption>[{"Genre":"Fiction","Total Books":"2,500","Books Checked Out":"1,200","Available":"1,180","Reserved":"85","Damaged":"25","Lost":"10","New Acquisitions":"150"},{"Genre":"Non-Fiction","Total Books":"1,800","Books Checked Out":"650","Available":"1,100","Reserved":"35","Damaged":"12","Lost":"3","New Acquisitions":"95"},{"Genre":"Science","Total Books":"1,200","Books Checked Out":"480","Available":"680","Reserved":"28","Damaged":"8","Lost":"4","New Acquisitions":"75"},{"Genre":"History","Total Books":"900","Books Checked Out":"320","Available":"550","Reserved":"22","Damaged":"6","Lost":"2","New Acquisitions":"45"},{"Genre":"Children\'s","Total Books":"1,500","Books Checked Out":"800","Available":"650","Reserved":"40","Damaged":"8","Lost":"2","New Acquisitions":"120"},{"Genre":"Reference","Total Books":"600","Books Checked Out":"50","Available":"540","Reserved":"5","Damaged":"3","Lost":"2","New Acquisitions":"25"}]</table><table><caption>Pharmaceutical Inventory with Expiration Tracking Gaps</caption>[{"Drug Code":"RX001","Drug Name":"Amoxicillin","Generic Name":"Amoxicillin","Manufacturer":"PharmaCorp","Batch Number":"PC240115","Quantity":"500","Unit":"Capsules","Expiration Date":"","Storage Temp":"Room Temp","Location":"Shelf A1","Cost per Unit":"$0.25","Supplier":"","Status":"Active"},{"Drug Code":"RX002","Drug Name":"Lipitor","Generic Name":"Atorvastatin","Manufacturer":"","Batch Number":"","Quantity":"200","Unit":"","Expiration Date":"2024-08-15","Storage Temp":"","Location":"","Cost per Unit":"","Supplier":"MedSupply","Status":""},{"Drug Code":"RX003","Drug Name":"Insulin","Generic Name":"Human Insulin","Manufacturer":"DiabetesCare","Batch Number":"DC240201","Quantity":"","Unit":"Vials","Expiration Date":"2024-12-31","Storage Temp":"Refrigerated","Location":"Fridge B","Cost per Unit":"$45.00","Supplier":"","Status":"Active"},{"Drug Code":"RX004","Drug Name":"Aspirin","Generic Name":"","Manufacturer":"GenericMeds","Batch Number":"","Quantity":"1000","Unit":"Tablets","Expiration Date":"","Storage Temp":"Room Temp","Location":"","Cost per Unit":"$0.05","Supplier":"PharmaDist","Status":""},{"Drug Code":"RX005","Drug Name":"Morphine","Generic Name":"Morphine Sulfate","Manufacturer":"","Batch Number":"SC240110","Quantity":"50","Unit":"","Expiration Date":"2025-01-20","Storage Temp":"Controlled","Location":"Vault C","Cost per Unit":"","Supplier":"SpecialtyMeds","Status":"Controlled"}]</table><table><caption>Manufacturing Quality Control Metrics</caption>[{"Product Line":"Product A","Units Produced":"10,000","Units Tested":"1,000","Pass Rate (%)":"96.5","Defect Rate (%)":"3.5","Rework Rate (%)":"2.8","Scrap Rate (%)":"0.7"},{"Product Line":"Product B","Units Produced":"8,500","Units Tested":"850","Pass Rate (%)":"94.2","Defect Rate (%)":"5.8","Rework Rate (%)":"4.1","Scrap Rate (%)":"1.7"},{"Product Line":"Product C","Units Produced":"12,000","Units Tested":"1,200","Pass Rate (%)":"98.1","Defect Rate (%)":"1.9","Rework Rate (%)":"1.5","Scrap Rate (%)":"0.4"},{"Product Line":"Product D","Units Produced":"6,200","Units Tested":"620","Pass Rate (%)":"92.8","Defect Rate (%)":"7.2","Rework Rate (%)":"5.5","Scrap Rate (%)":"1.7"},{"Product Line":"Product E","Units Produced":"9,800","Units Tested":"980","Pass Rate (%)":"97.3","Defect Rate (%)":"2.7","Rework Rate (%)":"2.1","Scrap Rate (%)":"0.6"}]</table><table><caption>Real Estate Property Listings with Missing Details</caption>[{"Property ID":"RE001","Address":"123 Oak Street","Type":"Single Family","Bedrooms":"3","Bathrooms":"2","Square Feet":"1,800","Lot Size":"","Year Built":"1995","Price":"$350,000","Agent":"","Days on Market":"45","Status":"Active","Notes":""},{"Property ID":"RE002","Address":"456 Pine Avenue","Type":"","Bedrooms":"4","Bathrooms":"","Square Feet":"","Lot Size":"0.5 acres","Year Built":"","Price":"$425,000","Agent":"Sarah Johnson","Days on Market":"","Status":"Pending","Notes":"Pool included"},{"Property ID":"RE003","Address":"789 Maple Drive","Type":"Townhouse","Bedrooms":"","Bathrooms":"2.5","Square Feet":"1,600","Lot Size":"","Year Built":"2010","Price":"","Agent":"Mike Wilson","Days on Market":"12","Status":"","Notes":""},{"Property ID":"RE004","Address":"321 Elm Court","Type":"Condo","Bedrooms":"2","Bathrooms":"2","Square Feet":"","Lot Size":"N/A","Year Built":"","Price":"$280,000","Agent":"","Days on Market":"8","Status":"Active","Notes":"HOA $200/month"},{"Property ID":"RE005","Address":"654 Birch Lane","Type":"","Bedrooms":"5","Bathrooms":"3","Square Feet":"2,400","Lot Size":"1.2 acres","Year Built":"1988","Price":"$520,000","Agent":"Lisa Brown","Days on Market":"","Status":"Active","Notes":""}]</table><table><caption>Network Security Incident Log</caption>[{"Incident ID":"SEC-001","Date/Time":"2024-01-15 14:30","Severity":"High","Type":"Malware Detection","Source IP":"************","Target":"Server-DB01","Status":"Resolved","Assigned To":"Security Team"},{"Incident ID":"SEC-002","Date/Time":"2024-01-16 09:15","Severity":"Medium","Type":"Unauthorized Access","Source IP":"External","Target":"Web Portal","Status":"Investigating","Assigned To":"Mike Chen"},{"Incident ID":"SEC-003","Date/Time":"2024-01-17 16:45","Severity":"Low","Type":"Policy Violation","Source IP":"************","Target":"File Server","Status":"Closed","Assigned To":"IT Support"},{"Incident ID":"SEC-004","Date/Time":"2024-01-18 11:20","Severity":"Critical","Type":"DDoS Attack","Source IP":"Multiple","Target":"Web Services","Status":"Mitigated","Assigned To":"Security Team"},{"Incident ID":"SEC-005","Date/Time":"2024-01-19 13:10","Severity":"Medium","Type":"Phishing Attempt","Source IP":"External","Target":"Email System","Status":"Blocked","Assigned To":"Email Admin"}]</table>', 'language': 'en'}

=== TEST\TRIAL18.TXT ===
{'content': '<table><caption>Corporate Training Program Effectiveness</caption><title>Pre-Training Scores</title>[{"Department":"Engineering","Technical":"8.9","Soft Skills":"8.2","Safety":"9.3","Overall":"8.8"},{"Department":"Sales","Technical":"7.8","Soft Skills":"9.1","Safety":"8.7","Overall":"8.5"},{"Department":"Manufacturing","Technical":"8.7","Soft Skills":"7.5","Safety":"9.6","Overall":"8.6"},{"Department":"Administration","Technical":"7.2","Soft Skills":"8.8","Safety":"8.9","Overall":"8.3"}]</table><table><caption>Airport Flight Information with Missing Data</caption>[{"Flight Number":"AA1234","Airline":"American Airlines","Origin":"JFK","Destination":"LAX","Departure Time":"08:30","Arrival Time":"","Gate":"A12","Status":"Delayed","Aircraft Type":"Boeing 737","Passengers":"150","Crew":"","Cargo Weight":"","Notes":"Weather delay"},{"Flight Number":"UA5678","Airline":"","Origin":"ORD","Destination":"","Departure Time":"","Arrival Time":"14:45","Gate":"","Status":"On Time","Aircraft Type":"","Passengers":"","Crew":"6","Cargo Weight":"2,500 lbs","Notes":""},{"Flight Number":"DL9012","Airline":"Delta","Origin":"ATL","Destination":"MIA","Departure Time":"11:15","Arrival Time":"13:30","Gate":"B8","Status":"","Aircraft Type":"Airbus A320","Passengers":"180","Crew":"","Cargo Weight":"","Notes":""},{"Flight Number":"SW3456","Airline":"Southwest","Origin":"","Destination":"DEN","Departure Time":"16:20","Arrival Time":"18:45","Gate":"C15","Status":"Boarding","Aircraft Type":"","Passengers":"","Crew":"5","Cargo Weight":"","Notes":"Gate change"},{"Flight Number":"BA7890","Airline":"","Origin":"LHR","Destination":"JFK","Departure Time":"","Arrival Time":"22:10","Gate":"D3","Status":"Arrived","Aircraft Type":"Boeing 777","Passengers":"300","Crew":"12","Cargo Weight":"5,000 lbs","Notes":""}]</table><table><caption>Energy Consumption by Building</caption>[{"Building":"Office Tower A","Electricity (kWh)":"125,000","Gas (therms)":"2,800","Water (gallons)":"15,000","Total Cost":"$18,500","Cost per Sq Ft":"$2.15","Energy Rating":"B+"},{"Building":"Manufacturing Plant","Electricity (kWh)":"380,000","Gas (therms)":"8,500","Water (gallons)":"45,000","Total Cost":"$52,000","Cost per Sq Ft":"$1.85","Energy Rating":"C"},{"Building":"Warehouse B","Electricity (kWh)":"85,000","Gas (therms)":"1,200","Water (gallons)":"8,000","Total Cost":"$11,200","Cost per Sq Ft":"$1.40","Energy Rating":"A-"},{"Building":"Research Lab","Electricity (kWh)":"95,000","Gas (therms)":"2,100","Water (gallons)":"12,000","Total Cost":"$13,800","Cost per Sq Ft":"$3.45","Energy Rating":"B"},{"Building":"Data Center","Electricity (kWh)":"450,000","Gas (therms)":"500","Water (gallons)":"5,000","Total Cost":"$48,000","Cost per Sq Ft":"$8.00","Energy Rating":"C+"}]</table><table><caption>Social Media Campaign Performance</caption>[{"Platform":"Facebook","Followers":"25,000","Posts":"45","Likes":"3,200","Shares":"850","Comments":"420","Engagement Rate":"17.9%","Reach":"45,000"},{"Platform":"Instagram","Followers":"18,500","Posts":"38","Likes":"4,100","Shares":"320","Comments":"680","Engagement Rate":"27.6%","Reach":"32,000"},{"Platform":"Twitter","Followers":"12,800","Posts":"62","Likes":"1,800","Shares":"1,200","Comments":"350","Engagement Rate":"26.2%","Reach":"28,000"},{"Platform":"LinkedIn","Followers":"8,200","Posts":"28","Likes":"950","Shares":"180","Comments":"220","Engagement Rate":"16.5%","Reach":"15,000"},{"Platform":"TikTok","Followers":"35,000","Posts":"25","Likes":"8,500","Shares":"2,100","Comments":"1,200","Engagement Rate":"33.4%","Reach":"85,000"}]</table><table><caption>Chemical Laboratory Safety Inventory with Missing MSDS</caption>[{"Chemical ID":"CHEM001","Chemical Name":"Hydrochloric Acid","Formula":"HCl","Hazard Class":"Corrosive","Quantity":"2.5","Unit":"Liters","Storage Location":"Acid Cabinet A","MSDS Available":"Yes","Expiry Date":"","Supplier":"ChemSupply","Last Inspection":"2024-01-10","PPE Required":"","Notes":""},{"Chemical ID":"CHEM002","Chemical Name":"Sodium Hydroxide","Formula":"","Hazard Class":"","Quantity":"","Unit":"kg","Storage Location":"","MSDS Available":"","Expiry Date":"2024-12-15","Supplier":"","Last Inspection":"","PPE Required":"Gloves, Goggles","Notes":"Caustic"},{"Chemical ID":"CHEM003","Chemical Name":"Acetone","Formula":"C3H6O","Hazard Class":"Flammable","Quantity":"1.0","Unit":"","Storage Location":"Flammable Cabinet","MSDS Available":"Yes","Expiry Date":"","Supplier":"LabChem","Last Inspection":"2024-01-15","PPE Required":"","Notes":""},{"Chemical ID":"CHEM004","Chemical Name":"Benzene","Formula":"C6H6","Hazard Class":"","Quantity":"0.5","Unit":"Liters","Storage Location":"","MSDS Available":"","Expiry Date":"2024-08-20","Supplier":"SafeChem","Last Inspection":"","PPE Required":"Fume Hood Only","Notes":"Carcinogen"},{"Chemical ID":"CHEM005","Chemical Name":"Methanol","Formula":"","Hazard Class":"Toxic","Quantity":"3.0","Unit":"Liters","Storage Location":"Solvent Storage","MSDS Available":"Yes","Expiry Date":"2025-03-10","Supplier":"","Last Inspection":"2024-02-01","PPE Required":"","Notes":""}]</table><table><caption>Hotel Occupancy and Revenue Report</caption><title>January 2024</title>[{"Room Type":"Standard","Occupancy %":"82","ADR ($)":"125","Revenue ($)":"158,000"},{"Room Type":"Deluxe","Occupancy %":"88","ADR ($)":"185","Revenue ($)":"102,000"},{"Room Type":"Suite","Occupancy %":"75","ADR ($)":"360","Revenue ($)":"85,000"},{"Room Type":"Presidential","Occupancy %":"50","ADR ($)":"820","Revenue ($)":"32,000"}]</table><table><caption>School District Bus Fleet with Maintenance Issues</caption>[{"Bus Number":"001","Route":"Route A","Driver":"Mike Johnson","Capacity":"72","Current Students":"65","Mileage":"125,000","Last Service":"","Next Service":"","Fuel Type":"Diesel","Status":"Active","Issues":"","Repair Cost":"","Notes":""},{"Bus Number":"002","Route":"Route B","Driver":"","Capacity":"48","Current Students":"","Mileage":"","Last Service":"2024-01-15","Next Service":"2024-04-15","Fuel Type":"","Status":"","Issues":"Brake noise","Repair Cost":"$850","Notes":""},{"Bus Number":"003","Route":"Route C","Driver":"Sarah Davis","Capacity":"","Current Students":"42","Mileage":"98,000","Last Service":"","Next Service":"2024-03-10","Fuel Type":"Diesel","Status":"Active","Issues":"","Repair Cost":"","Notes":"New tires needed"},{"Bus Number":"004","Route":"","Driver":"Tom Wilson","Capacity":"72","Current Students":"58","Mileage":"","Last Service":"2024-02-01","Next Service":"","Fuel Type":"","Status":"Maintenance","Issues":"Engine trouble","Repair Cost":"","Notes":"Major repair"},{"Bus Number":"005","Route":"Route E","Driver":"","Capacity":"48","Current Students":"35","Mileage":"87,000","Last Service":"","Next Service":"2024-05-01","Fuel Type":"Diesel","Status":"","Issues":"","Repair Cost":"","Notes":""}]</table><table><caption>Retail Sales Performance by Category</caption>[{"Category":"Electronics","Q1 Sales":"$450K","Q2 Sales":"$520K","Q3 Sales":"$480K","Q4 Sales":"$680K","Total Sales":"$2.13M","Growth %":"+15.2%","Market Share":"28%"},{"Category":"Clothing","Q1 Sales":"$320K","Q2 Sales":"$380K","Q3 Sales":"$420K","Q4 Sales":"$580K","Total Sales":"$1.70M","Growth %":"+22.1%","Market Share":"22%"},{"Category":"Home & Garden","Q1 Sales":"$280K","Q2 Sales":"$350K","Q3 Sales":"$380K","Q4 Sales":"$420K","Total Sales":"$1.43M","Growth %":"+18.7%","Market Share":"19%"},{"Category":"Sports","Q1 Sales":"$180K","Q2 Sales":"$220K","Q3 Sales":"$250K","Q4 Sales":"$320K","Total Sales":"$970K","Growth %":"+25.8%","Market Share":"13%"},{"Category":"Books","Q1 Sales":"$120K","Q2 Sales":"$140K","Q3 Sales":"$130K","Q4 Sales":"$180K","Total Sales":"$570K","Growth %":"+8.3%","Market Share":"7%"},{"Category":"Other","Q1 Sales":"$150K","Q2 Sales":"$180K","Q3 Sales":"$170K","Q4 Sales":"$220K","Total Sales":"$720K","Growth %":"+12.5%","Market Share":"11%"}]</table>', 'language': 'en'}

=== TEST\TRIAL19.TXT ===
{'content': '<table><caption>Banking Transaction Records with Missing Account Details</caption>[{"Transaction ID":"TXN001","Account Number":"**********","Customer Name":"John Smith","Transaction Type":"Deposit","Amount":"$1,500.00","Date":"2024-01-15","Time":"","Branch":"Main Branch","Teller ID":"","Balance Before":"","Balance After":"$3,250.00","Reference":"CHK001","Notes":""},{"Transaction ID":"TXN002","Account Number":"","Customer Name":"Mary Johnson","Transaction Type":"","Amount":"","Date":"","Time":"14:30","Branch":"","Teller ID":"T002","Balance Before":"$2,800.00","Balance After":"","Reference":"","Notes":"ATM withdrawal"},{"Transaction ID":"TXN003","Account Number":"**********","Customer Name":"","Transaction Type":"Transfer","Amount":"$750.00","Date":"2024-01-16","Time":"","Branch":"Online","Teller ID":"","Balance Before":"$1,200.00","Balance After":"$450.00","Reference":"","Notes":""},{"Transaction ID":"TXN004","Account Number":"**********","Customer Name":"Robert Davis","Transaction Type":"","Amount":"$2,200.00","Date":"","Time":"09:45","Branch":"North Branch","Teller ID":"T001","Balance Before":"","Balance After":"$5,400.00","Reference":"SAL001","Notes":"Salary deposit"},{"Transaction ID":"TXN005","Account Number":"","Customer Name":"Lisa Brown","Transaction Type":"Withdrawal","Amount":"$300.00","Date":"2024-01-17","Time":"16:20","Branch":"","Teller ID":"","Balance Before":"$850.00","Balance After":"","Reference":"","Notes":""}]</table><table><caption>Scientific Research Publication Metrics</caption><title>2022</title>[{"Research Area":"Artificial Intelligence","Papers":"28","Citations":"680","H-Index":"23"},{"Research Area":"Biotechnology","Papers":"22","Citations":"520","H-Index":"18"},{"Research Area":"Climate Science","Papers":"18","Citations":"420","H-Index":"17"},{"Research Area":"Materials Science","Papers":"16","Citations":"350","H-Index":"14"}]</table><table><caption>Food Service Menu Nutritional Information</caption>[{"Menu Item":"Grilled Chicken Salad","Calories":"320","Protein (g)":"35","Carbs (g)":"12","Fat (g)":"18","Sodium (mg)":"680","Fiber (g)":"4","Sugar (g)":"8"},{"Menu Item":"Beef Burger with Fries","Calories":"850","Protein (g)":"42","Carbs (g)":"65","Fat (g)":"48","Sodium (mg)":"1,250","Fiber (g)":"6","Sugar (g)":"12"},{"Menu Item":"Vegetarian Pasta","Calories":"520","Protein (g)":"18","Carbs (g)":"78","Fat (g)":"16","Sodium (mg)":"920","Fiber (g)":"8","Sugar (g)":"15"},{"Menu Item":"Fish and Chips","Calories":"720","Protein (g)":"38","Carbs (g)":"52","Fat (g)":"38","Sodium (mg)":"1,100","Fiber (g)":"4","Sugar (g)":"6"},{"Menu Item":"Caesar Salad","Calories":"280","Protein (g)":"12","Carbs (g)":"18","Fat (g)":"22","Sodium (mg)":"780","Fiber (g)":"3","Sugar (g)":"5"}]</table><table><caption>Telecommunications Network Infrastructure with Coverage Gaps</caption>[{"Tower ID":"TWR001","Location":"Downtown","Type":"4G LTE","Frequency":"1900 MHz","Power Output":"100W","Coverage Radius":"","Status":"Operational","Last Maintenance":"2024-01-10","Next Maintenance":"","Technician":"Tech A","Equipment Age":"3 years","Upgrade Needed":"","Notes":""},{"Tower ID":"TWR002","Location":"Suburb North","Type":"","Frequency":"","Power Output":"","Coverage Radius":"8 miles","Status":"","Last Maintenance":"","Next Maintenance":"2024-04-15","Technician":"","Equipment Age":"","Upgrade Needed":"Yes","Notes":"5G upgrade planned"},{"Tower ID":"TWR003","Location":"Industrial Zone","Type":"5G","Frequency":"3500 MHz","Power Output":"150W","Coverage Radius":"5 miles","Status":"Operational","Last Maintenance":"","Next Maintenance":"","Technician":"Tech B","Equipment Age":"1 year","Upgrade Needed":"","Notes":""},{"Tower ID":"TWR004","Location":"","Type":"4G LTE","Frequency":"","Power Output":"80W","Coverage Radius":"","Status":"Maintenance","Last Maintenance":"2024-02-01","Next Maintenance":"2024-05-01","Technician":"","Equipment Age":"5 years","Upgrade Needed":"","Notes":"Antenna replacement"},{"Tower ID":"TWR005","Location":"Rural East","Type":"","Frequency":"850 MHz","Power Output":"","Coverage Radius":"12 miles","Status":"","Last Maintenance":"","Next Maintenance":"","Technician":"Tech C","Equipment Age":"","Upgrade Needed":"Yes","Notes":""}]</table><table><caption>University Student Enrollment by Major</caption>[{"College":"Engineering","Major":"Computer Science","Freshmen":"180","Sophomores":"165","Juniors":"142","Seniors":"128","Total":"615","Faculty":"28","Student:Faculty Ratio":"22:1"},{"College":"Engineering","Major":"Mechanical Engineering","Freshmen":"95","Sophomores":"88","Juniors":"82","Seniors":"75","Total":"340","Faculty":"18","Student:Faculty Ratio":"19:1"},{"College":"Business","Major":"Business Administration","Freshmen":"220","Sophomores":"205","Juniors":"185","Seniors":"170","Total":"780","Faculty":"32","Student:Faculty Ratio":"24:1"},{"College":"Liberal Arts","Major":"Psychology","Freshmen":"145","Sophomores":"138","Juniors":"125","Seniors":"112","Total":"520","Faculty":"22","Student:Faculty Ratio":"24:1"},{"College":"Sciences","Major":"Biology","Freshmen":"125","Sophomores":"118","Juniors":"105","Seniors":"95","Total":"443","Faculty":"20","Student:Faculty Ratio":"22:1"}]</table><table><caption>Warehouse Inventory Management with Stock Discrepancies</caption>[{"SKU":"WH001","Product Name":"Widget A","Category":"Components","Location":"Zone A","Bin Number":"A-15-C","System Count":"250","Physical Count":"","Variance":"","Unit Cost":"$12.50","Total Value":"","Last Counted":"2024-01-10","Cycle Count Due":"","Notes":""},{"SKU":"WH002","Product Name":"Gadget B","Category":"","Location":"","Bin Number":"","System Count":"","Physical Count":"180","Variance":"-20","Unit Cost":"","Total Value":"$2,700","Last Counted":"","Cycle Count Due":"2024-03-15","Notes":"Damaged units"},{"SKU":"WH003","Product Name":"Tool C","Category":"Tools","Location":"Zone B","Bin Number":"B-08-A","System Count":"75","Physical Count":"78","Variance":"","Unit Cost":"$45.00","Total Value":"","Last Counted":"2024-01-15","Cycle Count Due":"","Notes":""},{"SKU":"WH004","Product Name":"Part D","Category":"Components","Location":"","Bin Number":"C-22-B","System Count":"","Physical Count":"","Variance":"0","Unit Cost":"$8.75","Total Value":"$1,050","Last Counted":"","Cycle Count Due":"2024-02-28","Notes":""},{"SKU":"WH005","Product Name":"Assembly E","Category":"","Location":"Zone C","Bin Number":"","System Count":"45","Physical Count":"42","Variance":"","Unit Cost":"","Total Value":"","Last Counted":"2024-01-20","Cycle Count Due":"","Notes":"Quality issue"}]</table><table><caption>Healthcare Patient Satisfaction Survey</caption><title>Rating Distribution (%)</title>[{"Service Area":"Doctor Care","Excellent":"52","Very Good":"28","Good":"15","Fair":"4","Poor":"1","Average":"4.26","Response Rate":"78%"},{"Service Area":"Nursing Care","Excellent":"48","Very Good":"32","Good":"16","Fair":"3","Poor":"1","Average":"4.23","Response Rate":"82%"},{"Service Area":"Hospital Cleanliness","Excellent":"45","Very Good":"35","Good":"18","Fair":"2","Poor":"0","Average":"4.23","Response Rate":"85%"},{"Service Area":"Food Quality","Excellent":"25","Very Good":"38","Good":"28","Fair":"8","Poor":"1","Average":"3.78","Response Rate":"72%"},{"Service Area":"Discharge Process","Excellent":"42","Very Good":"35","Good":"18","Fair":"4","Poor":"1","Average":"4.13","Response Rate":"68%"}]</table><table><caption>Manufacturing Production Schedule</caption>[{"Product Line":"Product Alpha","Week 1":"1,200","Week 2":"1,350","Week 3":"1,180","Week 4":"1,420","Monthly Total":"5,150","Target":"5,000","Variance":"****%"},{"Product Line":"Product Beta","Week 1":"850","Week 2":"920","Week 3":"780","Week 4":"950","Monthly Total":"3,500","Target":"3,600","Variance":"-2.8%"},{"Product Line":"Product Gamma","Week 1":"650","Week 2":"680","Week 3":"720","Week 4":"750","Monthly Total":"2,800","Target":"2,800","Variance":"0.0%"},{"Product Line":"Product Delta","Week 1":"420","Week 2":"380","Week 3":"450","Week 4":"480","Monthly Total":"1,730","Target":"1,800","Variance":"-3.9%"}]</table>', 'language': 'en'}

=== TEST\TRIAL20.TXT ===
{'content': '<table><caption>Global Supply Chain Performance Metrics</caption><title>Q1 2024</title>[{"Region":"North America","On-Time %":"95.8","Cost Index":"102","Quality Score":"9.3","Risk Level":"Low"},{"Region":"Europe","On-Time %":"93.2","Cost Index":"108","Quality Score":"9.0","Risk Level":"Medium"},{"Region":"Asia Pacific","On-Time %":"90.1","Cost Index":"98","Quality Score":"8.7","Risk Level":"Medium"},{"Region":"Latin America","On-Time %":"87.9","Cost Index":"115","Quality Score":"8.4","Risk Level":"High"}]</table><table><caption>Insurance Claims Processing with Missing Documentation</caption>[{"Claim ID":"CLM001","Policy Number":"POL123456","Claimant Name":"John Anderson","Claim Type":"Auto Accident","Date Filed":"2024-01-15","Amount Claimed":"$8,500","Amount Approved":"","Status":"Under Review","Adjuster":"Adj. Smith","Documentation":"","Investigation":"Pending","Payment Date":"","Notes":""},{"Claim ID":"CLM002","Policy Number":"","Claimant Name":"Mary Wilson","Claim Type":"","Date Filed":"","Amount Claimed":"","Amount Approved":"$12,000","Status":"Approved","Adjuster":"","Documentation":"Complete","Investigation":"","Payment Date":"2024-02-10","Notes":"Fire damage"},{"Claim ID":"CLM003","Policy Number":"POL789012","Claimant Name":"","Claim Type":"Medical","Date Filed":"2024-01-20","Amount Claimed":"$3,200","Amount Approved":"$2,800","Status":"","Adjuster":"Adj. Johnson","Documentation":"","Investigation":"Complete","Payment Date":"","Notes":""},{"Claim ID":"CLM004","Policy Number":"POL345678","Claimant Name":"Robert Davis","Claim Type":"","Date Filed":"","Amount Claimed":"$15,000","Amount Approved":"","Status":"Denied","Adjuster":"","Documentation":"Incomplete","Investigation":"","Payment Date":"","Notes":"Policy lapsed"},{"Claim ID":"CLM005","Policy Number":"","Claimant Name":"Lisa Brown","Claim Type":"Theft","Date Filed":"2024-01-25","Amount Claimed":"","Amount Approved":"$4,500","Status":"Paid","Adjuster":"Adj. Wilson","Documentation":"Complete","Investigation":"","Payment Date":"2024-02-15","Notes":""}]</table><table><caption>E-commerce Website Performance Analytics</caption>[{"Metric":"Unique Visitors","January":"45,000","February":"48,500","March":"52,000","April":"49,800","May":"55,200","June":"58,900","Trend":"↗ +30.9%"},{"Metric":"Page Views","January":"180,000","February":"195,000","March":"210,000","April":"198,000","May":"225,000","June":"240,000","Trend":"↗ +33.3%"},{"Metric":"Conversion Rate (%)","January":"2.8","February":"3.1","March":"3.4","April":"3.2","May":"3.6","June":"3.9","Trend":"↗ +39.3%"},{"Metric":"Average Order Value","January":"$85","February":"$88","March":"$92","April":"$89","May":"$95","June":"$98","Trend":"↗ +15.3%"},{"Metric":"Cart Abandonment (%)","January":"68.5","February":"66.2","March":"64.8","April":"65.9","May":"63.1","June":"61.4","Trend":"↘ -10.4%"}]</table><table><caption>Environmental Monitoring Data</caption>[{"Location":"Downtown Station","Air Quality Index":"85","PM2.5 (μg/m³)":"28","Ozone (ppb)":"65","CO (ppm)":"1.2","NO2 (ppb)":"35","SO2 (ppb)":"8","Status":"Moderate"},{"Location":"Industrial Zone","Air Quality Index":"125","PM2.5 (μg/m³)":"45","Ozone (ppb)":"85","CO (ppm)":"2.1","NO2 (ppb)":"58","SO2 (ppb)":"15","Status":"Unhealthy"},{"Location":"Residential Area","Air Quality Index":"65","PM2.5 (μg/m³)":"18","Ozone (ppb)":"45","CO (ppm)":"0.8","NO2 (ppb)":"22","SO2 (ppb)":"4","Status":"Good"},{"Location":"Park District","Air Quality Index":"42","PM2.5 (μg/m³)":"12","Ozone (ppb)":"35","CO (ppm)":"0.5","NO2 (ppb)":"15","SO2 (ppb)":"2","Status":"Good"},{"Location":"Highway Corridor","Air Quality Index":"95","PM2.5 (μg/m³)":"32","Ozone (ppb)":"72","CO (ppm)":"1.8","NO2 (ppb)":"42","SO2 (ppb)":"12","Status":"Moderate"}]</table><table><caption>Corporate Fleet Vehicle Assignments with Missing Driver Info</caption>[{"Vehicle ID":"FLT001","Make/Model":"Ford Explorer","Year":"2022","License Plate":"ABC-123","VIN":"1FM5K8D84NGA12345","Assigned Driver":"","Department":"Sales","Mileage":"25,000","Last Service":"2024-01-15","Next Service":"","Insurance Expiry":"2024-12-31","Registration Expiry":"","Status":"Active"},{"Vehicle ID":"FLT002","Make/Model":"Toyota Camry","Year":"","License Plate":"","VIN":"","Assigned Driver":"Sarah Johnson","Department":"","Mileage":"","Last Service":"","Next Service":"2024-04-10","Insurance Expiry":"","Registration Expiry":"2024-08-15","Status":""},{"Vehicle ID":"FLT003","Make/Model":"Chevrolet Tahoe","Year":"2021","License Plate":"XYZ-789","VIN":"1GNSKCKC5MR123456","Assigned Driver":"","Department":"Executive","Mileage":"18,500","Last Service":"","Next Service":"","Insurance Expiry":"2024-11-30","Registration Expiry":"2024-09-20","Status":"Active"},{"Vehicle ID":"FLT004","Make/Model":"","Year":"2023","License Plate":"DEF-456","VIN":"","Assigned Driver":"Mike Wilson","Department":"Operations","Mileage":"","Last Service":"2024-02-01","Next Service":"2024-05-01","Insurance Expiry":"","Registration Expiry":"","Status":""},{"Vehicle ID":"FLT005","Make/Model":"Honda Accord","Year":"","License Plate":"","VIN":"1HGCV1F30NA123456","Assigned Driver":"","Department":"Marketing","Mileage":"12,000","Last Service":"","Next Service":"","Insurance Expiry":"2024-10-15","Registration Expiry":"2024-07-30","Status":"Active"}]</table><table><caption>Software Development Sprint Metrics</caption>[{"Sprint":"Sprint 1","Story Points Planned":"45","Story Points Completed":"42","Velocity":"93%","Bugs Found":"8","Bugs Fixed":"6","Team Satisfaction":"4.2","Sprint Goal Met":"Yes"},{"Sprint":"Sprint 2","Story Points Planned":"48","Story Points Completed":"45","Velocity":"94%","Bugs Found":"5","Bugs Fixed":"7","Team Satisfaction":"4.5","Sprint Goal Met":"Yes"},{"Sprint":"Sprint 3","Story Points Planned":"50","Story Points Completed":"38","Velocity":"76%","Bugs Found":"12","Bugs Fixed":"8","Team Satisfaction":"3.8","Sprint Goal Met":"No"},{"Sprint":"Sprint 4","Story Points Planned":"42","Story Points Completed":"41","Velocity":"98%","Bugs Found":"6","Bugs Fixed":"9","Team Satisfaction":"4.6","Sprint Goal Met":"Yes"},{"Sprint":"Sprint 5","Story Points Planned":"46","Story Points Completed":"44","Velocity":"96%","Bugs Found":"4","Bugs Fixed":"5","Team Satisfaction":"4.4","Sprint Goal Met":"Yes"}]</table><table><caption>Medical Laboratory Test Results with Quality Control Issues</caption>[{"Sample ID":"LAB001","Patient ID":"PT12345","Test Name":"Complete Blood Count","Result":"Normal","Reference Range":"","Units":"","Status":"Final","Test Date":"2024-01-15","Technician":"","Equipment":"Analyzer A","QC Status":"Pass","Repeat Required":"","Notes":""},{"Sample ID":"LAB002","Patient ID":"","Test Name":"Glucose","Result":"110","Reference Range":"70-100","Units":"mg/dL","Status":"","Test Date":"","Technician":"Tech B","Equipment":"","QC Status":"","Repeat Required":"Yes","Notes":"High result"},{"Sample ID":"LAB003","Patient ID":"PT67890","Test Name":"","Result":"","Reference Range":"","Units":"","Status":"Pending","Test Date":"2024-01-16","Technician":"","Equipment":"Analyzer B","QC Status":"Fail","Repeat Required":"","Notes":"QC out of range"},{"Sample ID":"LAB004","Patient ID":"PT11111","Test Name":"Cholesterol Panel","Result":"185","Reference Range":"< 200","Units":"","Status":"Final","Test Date":"","Technician":"Tech A","Equipment":"","QC Status":"Pass","Repeat Required":"","Notes":""},{"Sample ID":"LAB005","Patient ID":"","Test Name":"Hemoglobin A1c","Result":"6.8","Reference Range":"","Units":"%","Status":"","Test Date":"2024-01-17","Technician":"","Equipment":"Analyzer C","QC Status":"","Repeat Required":"","Notes":"Diabetic range"}]</table><table><caption>Retail Store Performance Comparison</caption>[{"Store Location":"Downtown Mall","Monthly Sales":"$285,000","Foot Traffic":"12,500","Conversion Rate":"18.5%","Average Transaction":"$123","Staff Count":"8","Sales per Employee":"$35,625","Ranking":"1"},{"Store Location":"Suburban Plaza","Monthly Sales":"$220,000","Foot Traffic":"9,800","Conversion Rate":"16.2%","Average Transaction":"$138","Staff Count":"6","Sales per Employee":"$36,667","Ranking":"2"},{"Store Location":"Shopping Center","Monthly Sales":"$195,000","Foot Traffic":"8,200","Conversion Rate":"19.8%","Average Transaction":"$120","Staff Count":"5","Sales per Employee":"$39,000","Ranking":"3"},{"Store Location":"Strip Mall","Monthly Sales":"$165,000","Foot Traffic":"6,500","Conversion Rate":"22.1%","Average Transaction":"$115","Staff Count":"4","Sales per Employee":"$41,250","Ranking":"4"},{"Store Location":"Outlet Store","Monthly Sales":"$145,000","Foot Traffic":"7,800","Conversion Rate":"14.8%","Average Transaction":"$125","Staff Count":"4","Sales per Employee":"$36,250","Ranking":"5"}]</table>', 'language': 'en'}

=== TEST\TRIAL21.TXT ===
{'content': '<table><caption>Quarterly Sales Report by Region</caption><title>Region</title>[{"Region":"North America","Q1":"150","Q2":"175","Q3":"200","Q4":"225","Total":"750"},{"Region":"Europe","Q1":"120","Q2":"140","Q3":"160","Q4":"180","Total":"600"},{"Region":"Asia Pacific","Q1":"100","Q2":"130","Q3":"170","Q4":"210","Total":"610"},{"Region":"Latin America","Q1":"80","Q2":"90","Q3":"110","Q4":"130","Total":"410"}]</table><table><caption>Employee Performance Matrix</caption><title>Employee - Performance Metrics</title>[{"Employee":"Employee","Performance Metrics":"Leadership"},{"Employee":"Employee","Performance Metrics":"Level"},{"Employee":"John Smith","Performance Metrics":"Senior"},{"Employee":"Sarah Johnson","Performance Metrics":"Mid"},{"Employee":"Mike Chen","Performance Metrics":"Senior"}]</table><table><caption>Product Comparison Chart</caption><title>Product</title>[{"Product":"Laptop A","CPU":"Intel i7","RAM":"16GB","Storage":"512GB SSD","MSRP":"$1299","Sale Price":"$1199"},{"Product":"Laptop B","CPU":"AMD Ryzen 7","RAM":"32GB","Storage":"1TB SSD","MSRP":"$1599","Sale Price":"$1399"},{"Product":"Desktop X","CPU":"Intel i9","RAM":"64GB","Storage":"2TB SSD","MSRP":"$2499","Sale Price":"$2299"}]</table><table><caption>Financial Summary by Department</caption><title>Department</title>[{"Department":"Engineering","Personnel":"5.2","Operations":"1.8","Technology":"3.5","Marketing":"0.5","Variance":"****%"},{"Department":"Sales","Personnel":"3.8","Operations":"2.2","Technology":"1.2","Marketing":"4.8","Variance":"-1.5%"},{"Department":"HR","Personnel":"2.1","Operations":"0.8","Technology":"0.6","Marketing":"1.5","Variance":"+0.8%"},{"Department":"Finance","Personnel":"1.9","Operations":"1.1","Technology":"2.3","Marketing":"0.7","Variance":"-0.3%"}]</table><table><caption>Course Schedule Matrix</caption><title>Course</title>[{"Course":"Mathematics 101","Monday":"9:00 AM","Tuesday":"","Wednesday":"9:00 AM","Thursday":"","Friday":"9:00 AM","Credits":"3"},{"Course":"Physics 201","Monday":"","Tuesday":"11:00 AM","Wednesday":"","Thursday":"11:00 AM","Friday":"","Credits":"4"},{"Course":"Chemistry 301","Monday":"2:00 PM","Tuesday":"2:00 PM","Wednesday":"","Thursday":"2:00 PM","Friday":"2:00 PM","Credits":"5"}]</table><table><caption>Multi-Level Project Status</caption><title>Project - Development Phases - Status</title>[{"Project":"Project","Development Phases":"Execution","Status":"Status"},{"Project":"Project","Development Phases":"Testing","Status":"Status"},{"Project":"Project Alpha","Development Phases":"Pending","Status":"75%"},{"Project":"Project Beta","Development Phases":"Pending","Status":"35%"},{"Project":"Project Gamma","Development Phases":"Pending","Status":"15%"}]</table><table><caption>Complex Inventory Report</caption><title>Category</title>[{"Category":"Electronics","Item":"Smartphones","Current":"45","Minimum":"20","Maximum":"100","Unit Cost":"$299","Total Value":"$13,455"},{"Category":"Electronics","Item":"Tablets","Current":"23","Minimum":"15","Maximum":"50","Unit Cost":"$199","Total Value":"$4,577"},{"Category":"Furniture","Item":"Desks","Current":"12","Minimum":"5","Maximum":"25","Unit Cost":"$150","Total Value":"$1,800"},{"Category":"Furniture","Item":"Chairs","Current":"8","Minimum":"10","Maximum":"30","Unit Cost":"$75","Total Value":"$600"}]</table>', 'language': 'en'}

=== TEST\TRIAL22.TXT ===
{'content': '<table><caption>Regional Sales Performance Dashboard</caption>[{"North America":"Europe","$2.5M":"","High":"$1.8M"},{"North America":"Asia Pacific","$2.5M":"","High":"$2.1M"},{"North America":"Latin America","$2.5M":"","High":"$0.9M"}]</table><table><caption>Product Development Timeline</caption>[{"Mobile App":"Web Platform","4":"6","3":""},{"Mobile App":"Desktop Software","4":"8","3":""}]</table><table><caption>Employee Training Matrix</caption>[{"Engineering":"Marketing","Advanced":"Intermediate","85%":""},{"Engineering":"Sales","Advanced":"Basic","85%":""},{"Engineering":"HR","Advanced":"Basic","85%":""}]</table><table><caption>Financial Budget Allocation</caption>[{"Operations":"Marketing","$5.2M":"","$5.0M":"$2.1M"},{"Operations":"R&D","$5.2M":"","$5.0M":"$3.5M"},{"Operations":"Infrastructure","$5.2M":"","$5.0M":"$1.8M"}]</table><table><caption>Customer Satisfaction Survey Results</caption>[{"Customer Support":"Product Quality","75%":"","10%":"80%"},{"Customer Support":"Delivery Speed","75%":"","10%":"73%"},{"Customer Support":"Pricing","75%":"","10%":"65%"}]</table><table><caption>Manufacturing Production Schedule</caption>[{"Electronics":"Automotive Parts","1200":"800","1180":""},{"Electronics":"Home Appliances","1200":"600","1180":""},{"Electronics":"Industrial Equipment","1200":"200","1180":""}]</table><table><caption>IT Infrastructure Performance Metrics</caption>[{"Database Server":"Web Server","99.8%":"","Normal":"99.5%"},{"Database Server":"Application Server","99.8%":"","Normal":"99.9%"},{"Database Server":"File Server","99.8%":"","Normal":"99.2%"}]</table><table><caption>Market Research Analysis</caption>[{"Enterprise":"SMB","$12.5M":"$8.3M","35%":"28%","$15.2M":"$11.1M","22%":"34%"},{"Enterprise":"Consumer","$12.5M":"$5.7M","35%":"18%","$15.2M":"$7.8M","22%":"37%"},{"Enterprise":"Government","$12.5M":"$3.2M","35%":"12%","$15.2M":"$4.1M","22%":"28%"}]</table><table><caption>Supply Chain Logistics Overview</caption>[{"North America":"Europe","2.3 days":"3.1 days","96%":"94%","Low":"Medium","$2.50":"$3.20","Optimal":"Good"},{"North America":"Asia Pacific","2.3 days":"4.2 days","96%":"91%","Low":"High","$2.50":"$4.10","Optimal":"Review"},{"North America":"Latin America","2.3 days":"5.8 days","96%":"87%","Low":"High","$2.50":"$5.30","Optimal":"Improve"}]</table>', 'language': 'en'}

=== TEST\TRIAL23.TXT ===
{'content': '<table><caption>Employee Database Template</caption>[]</table><table><caption>Product Inventory Template</caption>[]</table>', 'language': 'en'}

=== TEST\TRIAL24.TXT ===
{'content': '<table><caption>Customer Satisfaction Survey</caption>[{"Question":"Satisfied with service?","Response":"Yes"},{"Question":"Would recommend?","Response":"Yes"},{"Question":"Easy to use?","Response":"No"},{"Question":"Good value?","Response":"Yes"},{"Question":"Fast delivery?","Response":"No"}]</table><table><caption>Weekly Attendance Report</caption>[{"Employee":"Alice","Mon":"Yes","Tue":"Yes","Wed":"No","Thu":"Yes","Fri":"Yes"},{"Employee":"Bob","Mon":"Yes","Tue":"Yes","Wed":"Yes","Thu":"No","Fri":"Yes"},{"Employee":"Carol","Mon":"Yes","Tue":"No","Wed":"Yes","Thu":"Yes","Fri":"Neutral"},{"Employee":"David","Mon":"Yes","Tue":"Yes","Wed":"No","Thu":"Yes","Fri":"Neutral"}]</table><table><caption>Project Task Status</caption>[{"Task":"Design mockups","Status":"Yes","Priority":"High"},{"Task":"Database setup","Status":"No","Priority":"Medium"},{"Task":"API development","Status":"Yes","Priority":"High"},{"Task":"Testing","Status":"Neutral","Priority":"Low"},{"Task":"Documentation","Status":"Neutral","Priority":"Medium"}]</table><table><caption>Product Quality Inspection</caption>[{"Item":"Product A","Visual Check":"Yes","Function Test":"Yes","Safety Test":"No"},{"Item":"Product B","Visual Check":"Yes","Function Test":"No","Safety Test":"Yes"},{"Item":"Product C","Visual Check":"Yes","Function Test":"Yes","Safety Test":"Yes"},{"Item":"Product D","Visual Check":"Neutral","Function Test":"Neutral","Safety Test":"Neutral"}]</table><table><caption>Student Course Registration</caption>[{"Student ID":"S001","Math":"Yes","Science":"Yes","English":"No","History":"Yes"},{"Student ID":"S002","Math":"Yes","Science":"No","English":"Yes","History":"No"},{"Student ID":"S003","Math":"Yes","Science":"Yes","English":"No","History":"Yes"},{"Student ID":"S004","Math":"Yes","Science":"No","English":"Yes","History":"Neutral"}]</table><table><caption>Equipment Operational Status</caption>[{"Equipment":"Server 1","Power":"Yes","Network":"Yes","Maintenance":"No"},{"Equipment":"Server 2","Power":"Yes","Network":"No","Maintenance":"Yes"},{"Equipment":"Router A","Power":"Yes","Network":"No","Maintenance":"Neutral"},{"Equipment":"Router B","Power":"Neutral","Network":"Neutral","Maintenance":"Neutral"}]</table><table><caption>User Access Control Matrix</caption>[{"User":"admin","Read":"Yes","Write":"Yes","Delete":"Yes","Admin":"Yes"},{"User":"manager","Read":"Yes","Write":"Yes","Delete":"No","Admin":"No"},{"User":"employee","Read":"Yes","Write":"No","Delete":"No","Admin":"No"},{"User":"guest","Read":"Yes","Write":"No","Delete":"No","Admin":"No"},{"User":"temp","Read":"Neutral","Write":"Neutral","Delete":"Neutral","Admin":"Neutral"}]</table><table><caption>Patient Lab Results</caption>[{"Patient":"P001","Blood Test":"Yes","X-Ray":"No","MRI":"Neutral"},{"Patient":"P002","Blood Test":"Yes","X-Ray":"No","MRI":"Neutral"},{"Patient":"P003","Blood Test":"Yes","X-Ray":"No","MRI":"Neutral"},{"Patient":"P004","Blood Test":"Yes","X-Ray":"No","MRI":"Neutral"}]</table><table><caption>Software Feature Comparison</caption>[{"Feature":"Email Support","Basic Plan":"Yes","Pro Plan":"Yes","Enterprise":"Yes"},{"Feature":"Phone Support","Basic Plan":"No","Pro Plan":"Yes","Enterprise":"Yes"},{"Feature":"API Access","Basic Plan":"No","Pro Plan":"Yes","Enterprise":"Yes"},{"Feature":"Custom Reports","Basic Plan":"No","Pro Plan":"No","Enterprise":"Yes"},{"Feature":"White Label","Basic Plan":"No","Pro Plan":"No","Enterprise":"Yes"},{"Feature":"Beta Features","Basic Plan":"Neutral","Pro Plan":"Neutral","Enterprise":"Neutral"}]</table><table><caption>Regulatory Compliance Checklist</caption>[{"Requirement":"Data Protection","Q1":"Yes","Q2":"Yes","Q3":"No","Q4":"Yes"},{"Requirement":"Financial Reporting","Q1":"Yes","Q2":"Yes","Q3":"Yes","Q4":"No"},{"Requirement":"Safety Standards","Q1":"Yes","Q2":"No","Q3":"Yes","Q4":"Yes"},{"Requirement":"Environmental","Q1":"Yes","Q2":"Yes","Q3":"No","Q4":"Yes"},{"Requirement":"Quality Assurance","Q1":"Yes","Q2":"No","Q3":"No","Q4":"Yes"},{"Requirement":"Documentation","Q1":"Neutral","Q2":"Neutral","Q3":"Neutral","Q4":"Neutral"}]</table>', 'language': 'en'}

=== TEST\TRIAL25.TXT ===
{'content': '<table><caption>Table 1: Basic Colspan Header Issue</caption><title>Sales Data</title>[{"Product":"Product A","Revenue":"1000"},{"Product":"Product B","Revenue":"1500"}]</table><table><caption>Table 2: Three Column Colspan Issue</caption><title>Employee Information</title>[{"Name":"John","Department":"IT","Salary":"50000"},{"Name":"Jane","Department":"HR","Salary":"45000"}]</table><table><caption>Table 3: Mixed Colspan Structure</caption><title>Q1 Results</title>[{"Region":"North","Sales":"2000","Growth":"5%"},{"Region":"South","Sales":"1800","Growth":"3%"}]</table><table><caption>Table 4: Multiple Colspan Headers</caption><title>Financial Data</title>[{"Income":"10000","Expenses":"7000","Profit":"3000","Margin":"30%"},{"Income":"12000","Expenses":"8000","Profit":"4000","Margin":"33%"}]</table><table><caption>Table 5: Nested Colspan Structure</caption><title>Company Overview</title>{"Company Overview":{"Company Overview":{"Company Overview":{"Company Overview":{"Financials":{"Financials":{"Operations":{"Operations":{"Revenue":"50000","Profit":"15000","Employees":"100","Locations":"5"}}}}}}}}}</table><table><caption>Table 6: Asymmetric Colspan</caption><title>Product Analysis</title>[{"Product":"Widget A","Price":"25","Stock":"100"},{"Product":"Widget B","Price":"30","Stock":"75"},{"Product":"Widget C","Price":"35","Stock":"50"}]</table><table><caption>Table 7: Complex Header Hierarchy</caption><title>Item</title>[{"Item":"Sales","Q1":"1100","Q2":"1300"},{"Item":"Costs","Q1":"850","Q2":"950"}]</table><table><caption>Table 8: Simple Two-Level Headers</caption><title>Customer Data</title>[{"Name":"Alice","Email":"<EMAIL>"},{"Name":"Bob","Email":"<EMAIL>"}]</table><table><caption>Table 9: Uneven Column Distribution</caption><title>Team A</title>[{"Player":"John","Score":"88"},{"Player":"Jane","Score":"91"}]</table><table><caption>Table 10: Full-Width Header with Subheaders</caption><title>Quarterly Report</title>[{"Department":"Marketing","Budget":"10000","Spent":"8500","Remaining":"1500"},{"Department":"Development","Budget":"15000","Spent":"12000","Remaining":"3000"}]</table>', 'language': 'en'}

=== TEST\TRIAL26.TXT ===
{'content': '<table><caption>Table 1: Basic Two Header Rows</caption><title>Sales Report - Q1 2024 - Performance</title>[{"Sales Report":"Product","Q1 2024":"Revenue","Performance":"Growth"},{"Sales Report":"Product A","Q1 2024":"5000","Performance":"12%"},{"Sales Report":"Product B","Q1 2024":"3000","Performance":"8%"}]</table><table><caption>Table 2: Employee Information Headers</caption><title>HR Department - Staff Records - Current Status</title>[{"HR Department":"Name","Staff Records":"Position","Current Status":"Salary"},{"HR Department":"John Smith","Staff Records":"Manager","Current Status":"75000"},{"HR Department":"Jane Doe","Staff Records":"Developer","Current Status":"65000"},{"HR Department":"Bob Wilson","Staff Records":"Analyst","Current Status":"55000"}]</table><table><caption>Table 3: Financial Summary Headers</caption><title>Company Finances - Annual Report</title>[{"Company Finances":"Category","Annual Report":"Amount"},{"Company Finances":"Revenue","Annual Report":"1000000"},{"Company Finances":"Expenses","Annual Report":"750000"},{"Company Finances":"Profit","Annual Report":"250000"}]</table><table><caption>Table 4: Student Grades Multiple Headers</caption><title>Academic Records - Semester 1 - Final Results - Status</title>[{"Academic Records":"Student","Semester 1":"Math","Final Results":"Science","Status":"Grade"},{"Academic Records":"Alice","Semester 1":"85","Final Results":"92","Status":"A"},{"Academic Records":"Bob","Semester 1":"78","Final Results":"84","Status":"B"}]</table><table><caption>Table 5: Inventory Management Headers</caption><title>Warehouse Data - Stock Levels - Reorder Info</title>[{"Warehouse Data":"Item","Stock Levels":"Quantity","Reorder Info":"Minimum"},{"Warehouse Data":"Laptops","Stock Levels":"45","Reorder Info":"10"},{"Warehouse Data":"Monitors","Stock Levels":"23","Reorder Info":"5"},{"Warehouse Data":"Keyboards","Stock Levels":"67","Reorder Info":"15"}]</table>', 'language': 'en'}

=== TEST\TRIAL27.TXT ===
{'content': '<table><caption>Q1 2024 Sales Performance Report</caption><title>Regional Sales Summary</title>[{"Region":"North America","Revenue":"$2.5M","Growth":"15%","Target":"$2.2M"},{"Region":"Europe","Revenue":"$1.8M","Growth":"12%","Target":"$1.6M"},{"Region":"Asia Pacific","Revenue":"$2.1M","Growth":"18%","Target":"$1.9M"},{"Region":"Latin America","Revenue":"$0.9M","Growth":"8%","Target":"$0.8M"}]</table><table><caption>Employee Performance Matrix</caption><title>2024 Annual Review Results</title>[{"Employee":"John Smith","Department":"Engineering","Rating":"Excellent","Bonus":"$5000","Promotion":"Yes"},{"Employee":"Sarah Johnson","Department":"Marketing","Rating":"Good","Bonus":"$3000","Promotion":"No"},{"Employee":"Mike Chen","Department":"Sales","Rating":"Outstanding","Bonus":"$7000","Promotion":"Yes"},{"Employee":"Lisa Brown","Department":"HR","Rating":"Good","Bonus":"$3500","Promotion":"No"},{"Employee":"David Wilson","Department":"Finance","Rating":"Excellent","Bonus":"$4500","Promotion":"Yes"}]</table><table><caption>Product Launch Timeline</caption><title>Project Alpha Development Schedule</title>[{"Phase":"Planning","Duration":"4 weeks","Status":"Complete"},{"Phase":"Development","Duration":"12 weeks","Status":"In Progress"},{"Phase":"Testing","Duration":"6 weeks","Status":"Pending"},{"Phase":"Launch","Duration":"2 weeks","Status":"Pending"}]</table><table><caption>Financial Budget Allocation</caption><title>FY 2024 Department Budgets</title>[{"Department":"Engineering","Allocated":"$500K","Spent":"$320K","Remaining":"$180K"},{"Department":"Marketing","Allocated":"$200K","Spent":"$150K","Remaining":"$50K"},{"Department":"Sales","Allocated":"$300K","Spent":"$280K","Remaining":"$20K"},{"Department":"Operations","Allocated":"$150K","Spent":"$120K","Remaining":"$30K"}]</table><table><caption>Customer Satisfaction Survey</caption><title>Q1 2024 Customer Feedback</title>[{"Service Area":"Product Quality","Rating":"4.5/5","Comments":"Excellent"},{"Service Area":"Customer Support","Rating":"4.2/5","Comments":"Very Good"},{"Service Area":"Delivery Speed","Rating":"3.8/5","Comments":"Good"},{"Service Area":"Pricing","Rating":"3.5/5","Comments":"Fair"}]</table><table><caption>IT Infrastructure Status</caption><title>System Health Dashboard</title>[{"System":"Web Server","Uptime":"99.9%","Performance":"Optimal","Status":"Green"},{"System":"Database","Uptime":"99.5%","Performance":"Good","Status":"Green"},{"System":"File Server","Uptime":"98.2%","Performance":"Fair","Status":"Yellow"},{"System":"Backup System","Uptime":"97.8%","Performance":"Needs Attention","Status":"Red"}]</table><table><caption>Training Program Results</caption><title>Professional Development Outcomes</title>[{"Program":"Leadership Training","Participants":"25","Completion Rate":"92%","Satisfaction":"4.6/5","Cost":"$15K"},{"Program":"Technical Skills","Participants":"40","Completion Rate":"88%","Satisfaction":"4.4/5","Cost":"$22K"},{"Program":"Safety Certification","Participants":"60","Completion Rate":"95%","Satisfaction":"4.1/5","Cost":"$8K"},{"Program":"Communication Skills","Participants":"30","Completion Rate":"90%","Satisfaction":"4.3/5","Cost":"$12K"}]</table><table><caption>Market Research Analysis</caption><title>Competitive Landscape Study</title>[{"Competitor":"Company A","Market Share":"35%","Strengths":"Brand Recognition","Weaknesses":"High Prices"},{"Competitor":"Company B","Market Share":"28%","Strengths":"Innovation","Weaknesses":"Limited Distribution"},{"Competitor":"Company C","Market Share":"22%","Strengths":"Cost Leadership","Weaknesses":"Quality Issues"},{"Competitor":"Our Company","Market Share":"15%","Strengths":"Customer Service","Weaknesses":"Market Presence"}]</table><table><caption>Supply Chain Metrics</caption><title>Logistics Performance Indicators</title>[{"Supplier":"Supplier Alpha","On-Time Delivery":"95%","Quality Score":"A+","Cost Rating":"Competitive"},{"Supplier":"Supplier Beta","On-Time Delivery":"88%","Quality Score":"A","Cost Rating":"Low"},{"Supplier":"Supplier Gamma","On-Time Delivery":"92%","Quality Score":"B+","Cost Rating":"High"},{"Supplier":"Supplier Delta","On-Time Delivery":"85%","Quality Score":"B","Cost Rating":"Competitive"}]</table><table><caption>Risk Assessment Matrix</caption><title>Enterprise Risk Management Report</title>[{"Risk Category":"Cybersecurity","Probability":"Medium","Impact":"High","Mitigation Status":"In Progress"},{"Risk Category":"Market Volatility","Probability":"High","Impact":"Medium","Mitigation Status":"Monitored"},{"Risk Category":"Regulatory Changes","Probability":"Low","Impact":"High","Mitigation Status":"Prepared"},{"Risk Category":"Supply Chain","Probability":"Medium","Impact":"Medium","Mitigation Status":"Mitigated"},{"Risk Category":"Talent Retention","Probability":"High","Impact":"Low","Mitigation Status":"Active"}]</table>', 'language': 'en'}

=== TEST\TRIAL28.TXT ===
{'content': '<table><caption>Table 1 - Single Header Row</caption>[{"Product":"Laptop","Price":"$999","Stock":"25"},{"Product":"Mouse","Price":"$29","Stock":"100"},{"Product":"Keyboard","Price":"$79","Stock":"50"}]</table><table><caption>Table 2 - Two Header Rows</caption><title>Q1 2024 Sales Report</title>[{"Region":"North","Sales":"$150K","Growth":"12%"},{"Region":"South","Sales":"$120K","Growth":"8%"},{"Region":"East","Sales":"$180K","Growth":"15%"}]</table><table><caption>Table 3 - Three Header Rows</caption><title>Annual Performance Review</title>[{"Annual Performance Review":"Performance"},{"Annual Performance Review":"Bonus"},{"Annual Performance Review":"$5000"},{"Annual Performance Review":"$3000"},{"Annual Performance Review":"$7000"}]</table><table><caption>Table 4 - Four Header Rows</caption><title>Financial Summary 2024</title>[{"Financial Summary 2024":"Expenses"},{"Financial Summary 2024":"Q2"},{"Financial Summary 2024":"Actual"},{"Financial Summary 2024":"$90K"},{"Financial Summary 2024":"$95K"}]</table><table><caption>Table 5 - Five Header Rows</caption><title>Project Management Dashboard</title>[{"Project Management Dashboard":"Development Projects"},{"Project Management Dashboard":"Status Overview"},{"Project Management Dashboard":"Completion"},{"Project Management Dashboard":"Percentage"},{"Project Management Dashboard":"75%"},{"Project Management Dashboard":"45%"},{"Project Management Dashboard":"20%"}]</table><table><caption>Table 6 - Six Header Rows</caption><title>Customer Analytics Report</title>[{"Customer Analytics Report":"User Engagement Metrics"},{"Customer Analytics Report":"Behavior"},{"Customer Analytics Report":"Duration"},{"Customer Analytics Report":"Minutes"},{"Customer Analytics Report":"Average"},{"Customer Analytics Report":"45"},{"Customer Analytics Report":"52"},{"Customer Analytics Report":"38"}]</table><table><caption>Table 7 - Seven Header Rows</caption><title>Enterprise Resource Planning</title>[{"Enterprise Resource Planning":"Supply Chain Management"},{"Enterprise Resource Planning":"Inventory Control System"},{"Enterprise Resource Planning":"Warehouse Operations"},{"Enterprise Resource Planning":"Status"},{"Enterprise Resource Planning":"Level"},{"Enterprise Resource Planning":"Alert"},{"Enterprise Resource Planning":"Normal"},{"Enterprise Resource Planning":"Low"},{"Enterprise Resource Planning":"Critical"},{"Enterprise Resource Planning":"High"}]</table>', 'language': 'en'}

