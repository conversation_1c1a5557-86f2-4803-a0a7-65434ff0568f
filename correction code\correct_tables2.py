
import os
import json

def expand_spans(table_with_span):
    expanded = []
    occupancy = {}  # (row, col): True if occupied

    for r_idx, row in enumerate(table_with_span):
        while len(expanded) <= r_idx:
            expanded.append([])

        c = 0
        for cell in row:
            while (r_idx, c) in occupancy:
                c += 1

            text = cell.get("text", None)
            rowspan = int(cell.get("rowspan", 1))
            colspan = int(cell.get("colspan", 1))

            for dr in range(rowspan):
                while len(expanded) <= r_idx + dr:
                    expanded.append([])

                for dc in range(colspan):
                    target_r = r_idx + dr
                    target_c = c + dc

                    while len(expanded[target_r]) <= target_c:
                        expanded[target_r].append(None)

                    expanded[target_r][target_c] = text
                    occupancy[(target_r, target_c)] = True

            c += colspan

    max_len = max(len(row) for row in expanded)
    for row in expanded:
        while len(row) < max_len:
            row.append(None)

    return expanded

def correct_table_json(input_path, output_path):
    with open(input_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    with_span = data.get("with_span", [])
    expanded = expand_spans(with_span)

    with open(output_path, "w", encoding="utf-8") as f:
        json.dump(expanded, f, indent=2, ensure_ascii=False)

def main():
    input_dir = "output"
    output_dir = "output2"

    # Create output2 folder if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    files = [f for f in os.listdir(input_dir) if f.endswith(".json")]

    for filename in files:
        input_path = os.path.join(input_dir, filename)
        output_path = os.path.join(output_dir, filename)
        print(f"Correcting {filename}...")
        correct_table_json(input_path, output_path)

    print(f"✅ Corrected {len(files)} tables and saved to '{output_dir}'.")

if __name__ == "__main__":
    main()
