
import json
from typing import List, Dict, Optional
import re

def is_empty_row(row: Dict) -> bool:
    return all(not (v or '').strip() for v in row.values())

def transform_generic_grouped_by_parent(data: List[Dict], parent_key: str = None, subtype_key: str = None, out_parent_key: str = None, out_subitems_key: str = None) -> List[Dict]:
    if not data:
        return []

    keys = list(data[0].keys())
    parent_key = parent_key or keys[0]
    subtype_key = subtype_key or keys[1]
    out_parent_key = out_parent_key or parent_key
    out_subitems_key = out_subitems_key or ("subitems" if "col" in parent_key else "Sub-items")

    output = []
    current_parent = None
    subitems = []

    for row in data:
        parent_val = (row.get(parent_key) or '').strip()
        subtype_val = (row.get(subtype_key) or '').strip()

        if parent_val and is_empty_row({k: v for k, v in row.items() if k != parent_key}):
            if current_parent and subitems:
                output.append({out_parent_key: current_parent, out_subitems_key: subitems})
            current_parent = parent_val
            subitems = []
        elif parent_val:
            if current_parent and subitems:
                output.append({out_parent_key: current_parent, out_subitems_key: subitems})
            current_parent = parent_val
            subitems = []
            entry = {"type": subtype_val} if subtype_val else {}
            entry.update({k: v for k, v in row.items() if k not in [parent_key, subtype_key]})
            subitems.append(entry)
        elif subtype_val:
            entry = {"type": subtype_val} if subtype_val else {}
            entry.update({k: v for k, v in row.items() if k not in [parent_key, subtype_key]})
            subitems.append(entry)
        else:
            flat = {k: v for k, v in row.items() if k != subtype_key}
            if subitems:
                output.append({out_parent_key: current_parent, out_subitems_key: subitems})
                current_parent = None
                subitems = []
            output.append(flat)

    if current_parent and subitems:
        output.append({out_parent_key: current_parent, out_subitems_key: subitems})

    return output

def transform_year_grouped_rows(data: List[Dict], year_key: str = None) -> List[Dict]:
    if not data:
        return []

    keys = list(data[0].keys())
    year_key = year_key or keys[0]
    output = []
    current_year = None
    group = []

    for row in data:
        year_val = (row.get(year_key) or '').strip()
        if year_val.isdigit():
            if current_year:
                output.append({"year": current_year, "data": group})
            current_year = year_val
            group = []
        else:
            item = {"type": year_val}
            item.update({k: v for k, v in row.items() if k != year_key})
            group.append(item)

    if current_year:
        output.append({"year": current_year, "data": group})

    return output

def transform_keyed_subitems(data: List[Dict], parent_key: str = None, subtype_key: str = None) -> Dict:
    if not data:
        return {}

    keys = list(data[0].keys())
    parent_key = parent_key or keys[0]
    subtype_key = subtype_key or keys[1]

    result = {}
    current_key = None

    for row in data:
        parent_val = (row.get(parent_key) or '').strip()
        subtype_val = (row.get(subtype_key) or '').strip()

        if parent_val:
            current_key = parent_val.lower().replace(" ", "_")
            result[current_key] = []

        if subtype_val:
            item = {"type": subtype_val}
            item.update({k: v for k, v in row.items() if k not in [parent_key, subtype_key]})
            result[current_key].append(item)

    return result

def is_likely_identifier(value: str) -> bool:
    if not value or not isinstance(value, str):
        return False
    stripped = value.strip()
    return (
        not stripped.isdigit() and
        not stripped.replace(",", "").replace(".", "").isdigit() and
        not (len(stripped) == 4 and stripped.isdigit())
    )

def detect_identifier_column(data: List[Dict]) -> Optional[str]:
    column_scores = {}
    for row in data:
        for k, v in row.items():
            if is_likely_identifier(v):
                column_scores[k] = column_scores.get(k, 0) + 1
    if not column_scores:
        return None
    return max(column_scores, key=column_scores.get)

def transform_col_indexed_json(
    data: List[Dict],
    ignore_cols: Optional[List[str]] = None
) -> Dict[str, Dict[str, Dict[str, str]]]:
    if ignore_cols is None:
        ignore_cols = []

    identifier_column = detect_identifier_column(data)
    if not identifier_column:
        return {}

    output = {}
    current_header = None

    for row in data:
        name = (row.get(identifier_column) or '').strip()
        values = {
            k: v.strip() for k, v in row.items()
            if k != identifier_column and k not in ignore_cols and isinstance(v, str)
        }

        if is_empty_row(values) and name:
            current_header = name
            output[current_header] = {}
        elif name and values:
            if current_header is None:
                raise ValueError("Subtype row encountered before any header")
            output[current_header][name] = values

    return output


def correct_year_columns2(data):
    corrected = []

    for row in data:
        new_row = {}
        new_row['col_0'] = row.get('col_0', '').strip()

        named_keys = [k for k in row if not re.fullmatch(r'col_\d+', k) and k != 'col_0']
        col_keys = sorted(
            [k for k in row if re.fullmatch(r'col_\d+', k) and k != 'col_0'],
            key=lambda x: int(x.split('_')[1])
        )

        for i, key in enumerate(named_keys):
            val_named = row.get(key, '').strip()
            val_col = row.get(col_keys[i], '').strip() if i < len(col_keys) else ''
            merged_val = val_named if val_named else val_col
            new_row[key] = merged_val

        corrected.append(new_row)

    return corrected

def transform_json_table(data):
    result = {}

    for row in data:
        row_label = row.get("col_0", "").strip()
        if not row_label:
            continue

        mapped = {}

        named_keys = [k for k in row if not re.fullmatch(r"col_\d+", k) and k != "col_0"]
        col_keys = sorted(
            [k for k in row if re.fullmatch(r"col_\d+", k) and k != "col_0"],
            key=lambda x: int(x.split("_")[1])
        )

        for i, col_key in enumerate(col_keys):
            value = row[col_key].strip()
            if i < len(named_keys):
                header = named_keys[i]
                if value:
                    value = value.strip("()")
                    mapped[header] = value

        for key in named_keys:
            val = row.get(key, "").strip()
            if val:
                val = val.strip("()")
                mapped[key] = val

        result[row_label] = mapped

    return result

def transform_json_table2(data: List[Dict[str, str]]) -> Dict[str, Dict[str, str]]:
    result = {}

    for row in data:
        row_label = row.get("col_0", "").strip()
        if not row_label:
            continue

        mapped = {}

        named_keys = [k for k in row if k != "col_0"]

        for key in named_keys:
            val = row.get(key, "").strip()
            if val:
                # Optional: handle parentheses (negatives) and commas
                if val.startswith("(") and val.endswith(")"):
                    val = val[1:-1]
                val = val.replace(",", "")
                mapped[key] = val

        result[row_label] = mapped

    return result


if __name__ == '__main__':

    with open('table_6.json', 'r', encoding='utf-8') as infile:
        input_data = json.load(infile)

    # Choose the transformation to apply
    # result = transform_generic_grouped_by_parent(input_data)    # For structure like 2,17,18,20,21
    result = transform_year_grouped_rows(input_data)            # For structure like 6,29
    # result = transform_keyed_subitems(input_data)               # For structure like 11,17,18
    # result = transform_col_indexed_json(input_data)             # For structure like 5,24,28,29
    # result= transform_json_table2(correct_year_columns2(input_data))                        # for 10
    # result=transform_table(input_data)
        
    with open('output.json', 'w', encoding='utf-8') as outfile:
        json.dump(result, outfile, indent=2, ensure_ascii=False)

    print("✅ Generic transformation complete using json.")
