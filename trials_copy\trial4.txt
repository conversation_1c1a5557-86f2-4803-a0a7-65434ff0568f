{
  'content': """
<table>
<caption>Research Study Results</caption>
<tr>
  <th>Study Group</th>
  <th>Sample Size</th>
  <th>Success Rate</th>
  <th>Confidence Level</th>
</tr>
<tr>
  <th>Control Group</th>
  <td>150</td>
  <td>72%</td>
  <td>95%</td>
</tr>
<tr>
  <th>Treatment A</th>
  <td>145</td>
  <td>84%</td>
  <td>95%</td>
</tr>
<tr>
  <th>Treatment B</th>
  <td>148</td>
  <td>79%</td>
  <td>95%</td>
</tr>
</table>

<table>
<caption>Manufacturing Production Line</caption>
<tr>
  <th rowspan="2">Production Line</th>
  <th colspan="3">Daily Output (Units)</th>
</tr>
<tr>
  <th>Monday</th>
  <th>Tuesday</th>
  <th>Wednesday</th>
</tr>
<tr>
  <td>Line A</td>
  <td>450</td>
  <td>480</td>
  <td>465</td>
</tr>
<tr>
  <td>Line B</td>
  <td>380</td>
  <td>395</td>
  <td>410</td>
</tr>
<tr>
  <td>Line C</td>
  <td>520</td>
  <td>535</td>
  <td>515</td>
</tr>
</table>

<table>
<caption>Customer Satisfaction Survey</caption>
<tr>
  <th>Service Category</th>
  <th>Excellent</th>
  <th>Good</th>
  <th>Fair</th>
  <th>Poor</th>
</tr>
<tr>
  <td>Customer Support</td>
  <td>◻</td>
  <td>☑</td>
  <td>☐</td>
  <td>☐</td>
</tr>
<tr>
  <td>Product Quality</td>
  <td>☑</td>
  <td>☐</td>
  <td>☐</td>
  <td>☐</td>
</tr>
<tr>
  <td>Delivery Time</td>
  <td>☐</td>
  <td>☑</td>
  <td>☐</td>
  <td>☐</td>
</tr>
<tr>
  <td>Pricing</td>
  <td>☐</td>
  <td>☐</td>
  <td>☑</td>
  <td>☐</td>
</tr>
</table>

<table>
<caption>Software Development Sprint</caption>
<tr>
  <th>Task ID</th>
  <th>Task Description</th>
  <th>Assigned To</th>
  <th>Status</th>
  <th>Priority</th>
</tr>
<tr>
  <td>TASK-001</td>
  <td>User Authentication Module</td>
  <td>Alice Johnson</td>
  <td>✅</td>
  <td>High</td>
</tr>
<tr>
  <td>TASK-002</td>
  <td>Database Schema Design</td>
  <td>Bob Smith</td>
  <td>✅</td>
  <td>High</td>
</tr>
<tr>
  <td>TASK-003</td>
  <td>API Endpoint Development</td>
  <td>Carol Davis</td>
  <td>❌</td>
  <td>Medium</td>
</tr>
<tr>
  <td>TASK-004</td>
  <td>Frontend UI Components</td>
  <td>David Wilson</td>
  <td>✅</td>
  <td>Medium</td>
</tr>
<tr>
  <td>TASK-005</td>
  <td>Testing Framework Setup</td>
  <td>Eve Brown</td>
  <td>❌</td>
  <td>Low</td>
</tr>
</table>

<table>
<caption>Energy Consumption Report</caption>
<tr>
  <th colspan="2">Building Section</th>
  <th>kWh Used</th>
  <th>Cost (USD)</th>
</tr>
<tr>
  <td rowspan="3">Office Floors</td>
  <td>Floor 1-5</td>
  <td>2400</td>
  <td>360</td>
</tr>
<tr>
  <td>Floor 6-10</td>
  <td>2200</td>
  <td>330</td>
</tr>
<tr>
  <td>Floor 11-15</td>
  <td>1800</td>
  <td>270</td>
</tr>
<tr>
  <td rowspan="2">Common Areas</td>
  <td>Lobby & Elevators</td>
  <td>800</td>
  <td>120</td>
</tr>
<tr>
  <td>Parking Garage</td>
  <td>600</td>
  <td>90</td>
</tr>
</table>

<table>
<caption>Medical Test Results</caption>
<tr>
  <th>Patient ID</th>
  <th>Blood Pressure</th>
  <th>Heart Rate</th>
  <th>Temperature</th>
  <th>Normal Range</th>
</tr>
<tr>
  <td>P001</td>
  <td>120/80</td>
  <td>72</td>
  <td>98.6°F</td>
  <td>✓</td>
</tr>
<tr>
  <td>P002</td>
  <td>140/90</td>
  <td>85</td>
  <td>99.2°F</td>
  <td>✗</td>
</tr>
<tr>
  <td>P003</td>
  <td>115/75</td>
  <td>68</td>
  <td>98.4°F</td>
  <td>✓</td>
</tr>
<tr>
  <td>P004</td>
  <td>130/85</td>
  <td>78</td>
  <td>98.8°F</td>
  <td>✓</td>
</tr>
</table>

<table>
<caption>Event Planning Checklist</caption>
<tr>
  <th>Task Category</th>
  <th>Task</th>
  <th>Deadline</th>
  <th>Completed</th>
</tr>
<tr>
  <td rowspan="3">Venue</td>
  <td>Book Conference Hall</td>
  <td>2024-06-15</td>
  <td>✔</td>
</tr>
<tr>
  <td>Setup Audio/Visual</td>
  <td>2024-06-20</td>
  <td>✔</td>
</tr>
<tr>
  <td>Arrange Seating</td>
  <td>2024-06-22</td>
  <td>✘</td>
</tr>
<tr>
  <td rowspan="2">Catering</td>
  <td>Select Menu</td>
  <td>2024-06-18</td>
  <td>✔</td>
</tr>
<tr>
  <td>Confirm Headcount</td>
  <td>2024-06-21</td>
  <td>✘</td>
</tr>
</table>
""",
  'language': 'en'
}
