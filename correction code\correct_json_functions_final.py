import json
from typing import List, Dict, Optional,Any,Union
import re

def table_2d_to_dicts(table: List[List[Optional[str]]]) -> List[Dict[str, Optional[str]]]:
    if not table or len(table) < 2:
        return []
    
    raw_headers = table[0]
    headers = []
    last_valid_header = None
    for h in raw_headers:
        if h is None or h.strip() == "":
            if last_valid_header is not None:
                headers.append(f"{last_valid_header}_2")
            else:
                headers.append("unnamed")
        else:
            last_valid_header = h.strip()
            headers.append(last_valid_header)

    result = []
    for row in table[1:]:
        padded_row = row + [None] * (len(headers) - len(row))
        row_dict = {headers[i]: padded_row[i] for i in range(len(headers))}
        result.append(row_dict)

    return result


def is_empty_row(row: Dict) -> bool:
    return all(not (v or '').strip() for v in row.values())

def transform_generic_grouped_by_parent(data: List[Dict], parent_key: str = None, subtype_key: str = None, out_parent_key: str = None, out_subitems_key: str = None) -> List[Dict]:
    if not data:
        return []

    keys = list(data[0].keys())
    parent_key = parent_key or keys[0]
    subtype_key = subtype_key or keys[1]
    out_parent_key = out_parent_key or parent_key
    out_subitems_key = out_subitems_key or ("subitems" if "col" in parent_key else "Sub-items")

    output = []
    current_parent = None
    subitems = []

    for row in data:
        parent_val = (row.get(parent_key) or '').strip()
        subtype_val = (row.get(subtype_key) or '').strip()

        if parent_val and is_empty_row({k: v for k, v in row.items() if k != parent_key}):
            if current_parent and subitems:
                output.append({out_parent_key: current_parent, out_subitems_key: subitems})
            current_parent = parent_val
            subitems = []
        elif parent_val:
            if current_parent and subitems:
                output.append({out_parent_key: current_parent, out_subitems_key: subitems})
            current_parent = parent_val
            subitems = []
            entry = {"type": subtype_val} if subtype_val else {}
            entry.update({k: v for k, v in row.items() if k not in [parent_key, subtype_key]})
            subitems.append(entry)
        elif subtype_val:
            entry = {"type": subtype_val} if subtype_val else {}
            entry.update({k: v for k, v in row.items() if k not in [parent_key, subtype_key]})
            subitems.append(entry)
        else:
            flat = {k: v for k, v in row.items() if k != subtype_key}
            if subitems:
                output.append({out_parent_key: current_parent, out_subitems_key: subitems})
                current_parent = None
                subitems = []
            output.append(flat)

    if current_parent and subitems:
        output.append({out_parent_key: current_parent, out_subitems_key: subitems})

    return output

def transform_year_grouped_rows(data: List[Dict], year_key: str = None) -> List[Dict]:
    if not data:
        return []

    keys = list(data[0].keys())
    year_key = year_key or keys[0]
    output = []
    current_year = None
    group = []

    for row in data:
        year_val = (row.get(year_key) or '').strip()
        if year_val.isdigit():
            if current_year:
                output.append({"year": current_year, "data": group})
            current_year = year_val
            group = []
        else:
            item = {"type": year_val}
            item.update({k: v for k, v in row.items() if k != year_key})
            group.append(item)

    if current_year:
        output.append({"year": current_year, "data": group})

    return output

def transform_keyed_subitems(data: List[Dict], parent_key: str = None, subtype_key: str = None) -> Dict:
    if not data:
        return {}

    keys = list(data[0].keys())
    parent_key = parent_key or keys[0]
    subtype_key = subtype_key or keys[1]

    result = {}
    current_key = None

    for row in data:
        parent_val = (row.get(parent_key) or '').strip()
        subtype_val = (row.get(subtype_key) or '').strip()

        if parent_val:
            current_key = parent_val.lower().replace(" ", "_")
            result[current_key] = []

        if subtype_val:
            item = {"type": subtype_val}
            item.update({k: v for k, v in row.items() if k not in [parent_key, subtype_key]})
            result[current_key].append(item)

    return result

def is_likely_identifier(value: str) -> bool:
    if not value or not isinstance(value, str):
        return False
    stripped = value.strip()
    return (
        not stripped.isdigit() and
        not stripped.replace(",", "").replace(".", "").isdigit() and
        not (len(stripped) == 4 and stripped.isdigit())
    )


def is_likely_identifier(value: Optional[str]) -> bool:
    if not value or not isinstance(value, str):
        return False
    value = value.strip()
    if value == "":
        return False
    if value.isdigit() and len(value) > 3:
        return False
    if len(value) > 50:
        return False
    if any(char.isalpha() for char in value):
        return True
    return False

def is_empty_row(values: Dict[str, Optional[Union[str, object]]]) -> bool:
    for v in values.values():
        if v is None:
            continue
        if isinstance(v, str) and v.strip() == "":
            continue
        return False
    return True

def detect_identifier_column(data: List[Dict[str, Optional[str]]]) -> Optional[str]:
    column_scores = {}
    for row in data:
        for k, v in row.items():
            if is_likely_identifier(v):
                column_scores[k] = column_scores.get(k, 0) + 1
    if not column_scores:
        return None
    return max(column_scores, key=column_scores.get)

def transform_col_indexed_json(
    data: List[Dict[str, Optional[str]]],
    identifier_column: Optional[str] = None,
    ignore_cols: Optional[List[str]] = None
) -> Dict[str, Dict[str, Dict[str, Optional[str]]]]:
    if not data:
        return {}

    if ignore_cols is None:
        ignore_cols = []

    if identifier_column is None:
        identifier_column = detect_identifier_column(data)
        if identifier_column is None:
            identifier_column = list(data[0].keys())[0]

    output = {}
    current_header = None

    for row in data:
        if identifier_column not in row:
            continue

        raw_name = row.get(identifier_column)
        name = raw_name.strip() if isinstance(raw_name, str) else str(raw_name) if raw_name is not None else ""

        values = {
            k: (v.strip() if isinstance(v, str) else v)
            for k, v in row.items()
            if k != identifier_column and k not in ignore_cols
        }

        if is_empty_row(values) and name:
            current_header = name
            output[current_header] = {}
        elif name and any(v not in [None, ""] for v in values.values()):
            if current_header is None:
                current_header = "_root"
                output[current_header] = {}
            output[current_header][name] = values

    return output

def clean_value(val: str) -> str:
    if not val or val.strip() == "":
        return ""
    val = val.strip()
    if val.startswith("(") and val.endswith(")"):
        val = "-" + val[1:-1]
    return val.replace(",", "")

def try_float(val: str) -> float:
    try:
        return float(val)
    except:
        return 0.0

def transform_json_table(data: List[Dict[str, str]]) -> Dict[str, Dict[str, str]]:
    result = {}
    current_parent = None
    headers = set()
    for row in data:
        for key in row:
            if key != "unnamed":
                base_key = key.replace("_2", "")
                headers.add(base_key)

    def row_is_empty(row):
        for key in headers:
            if clean_value(row.get(key, "")) or clean_value(row.get(f"{key}_2", "")):
                return False
        return True

    for row in data:
        label = row.get("unnamed", "").strip()
        if not label:
            continue  

        if row_is_empty(row):
            result[label] = {}
            current_parent = label
            continue

        row_data = {}
        for key in headers:
            val_main = clean_value(row.get(key, ""))
            val_alt = clean_value(row.get(f"{key}_2", ""))
            final_val = val_alt if val_alt else val_main

            if final_val != "":
                prev_val = row_data.get(key, "")
                prev_num = try_float(prev_val) if prev_val else 0.0
                curr_num = try_float(final_val)
                summed = prev_num + curr_num
                if summed == 0:
                    row_data[key] = ""
                elif summed.is_integer():
                    row_data[key] = str(int(summed))
                else:
                    row_data[key] = str(summed)
            else:
                row_data[key] = ""

        if current_parent and isinstance(result.get(current_parent), dict):
            result[current_parent][label] = row_data
        else:
            result[label] = row_data
            current_parent = None

    return result

def transform_year_columns_nested(data: List[Dict[str, Optional[str]]]) -> Dict[str, Dict[str, Dict[str, str]]]:
    if not data or len(data) < 2:
        return {}

    header_row = data[0]
    entity_key = next((k for k in header_row if k.lower() in ["", "unnamed", "name"]), list(header_row.keys())[0])

    output = {}

    for row in data[1:]:
        entity = row.get(entity_key, "")
        if not entity or not str(entity).strip():
            continue
        entity = str(entity).strip()
        output.setdefault(entity, {})

        for key, value in row.items():
            if key == entity_key:
                continue

            year = key.split("_")[0].strip()
            type_ = header_row.get(key, "").strip() if header_row.get(key) else ""
            if not year or not type_:
                continue

            output[entity].setdefault(year, {})
            val = str(value).strip() if value is not None else ""
            if val:
                output[entity][year][type_] = val

    return output

if __name__ == '__main__':

    with open('table_23.json', 'r', encoding='utf-8') as infile:
        input_data = json.load(infile)  

    data_dicts = table_2d_to_dicts(input_data)
    #print(data_dicts)
    #result = transform_generic_grouped_by_parent(data_dicts)    # For structure like 2,20,21
    #result = transform_year_grouped_rows(data_dicts)            # For structure like 6,29
    #result = transform_keyed_subitems(data_dicts)               # For structure like 11,17,18
    #result = transform_col_indexed_json(data_dicts)             # For structure like 5,24,28
    #result = transform_json_table(data_dicts)                   # for 10
    result=transform_year_columns_nested(data_dicts)             # for 12,23,25

with open('output.json', 'w', encoding='utf-8') as outfile:
    json.dump(result, outfile, indent=2, ensure_ascii=False)

print("✅ Generic transformation complete using json.")
