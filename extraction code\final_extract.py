import os
import json
from lxml import html, etree
from bs4 import BeautifulSoup

def extract_table_with_span_lxml(table_el):
    rows = table_el.xpath(".//tr")
    table_data = []
    for row in rows:
        row_data = []
        for cell in row.xpath("./th|./td"):
            row_data.append({
                "tag": cell.tag,
                "text": ''.join(cell.itertext()).strip(),
                "rowspan": int(cell.get("rowspan", "1")),
                "colspan": int(cell.get("colspan", "1"))
            })
        table_data.append(row_data)
    return table_data

def extract_table_with_span_bs4(table):
    rows = table.find_all("tr")
    table_data = []
    for row in rows:
        row_data = []
        for cell in row.find_all(["th", "td"]):
            row_data.append({
                "tag": cell.name,
                "text": cell.get_text(strip=True),
                "rowspan": int(cell.get("rowspan", 1)),
                "colspan": int(cell.get("colspan", 1))
            })
        table_data.append(row_data)
    return table_data

def process_tables_with_lxml(html_content):
    tree = html.fromstring(html_content)
    tables = tree.xpath("//table")
    output = []
    for idx, table in enumerate(tables, 1):
        caption_el = table.xpath("./caption")
        caption = caption_el[0].text.strip() if caption_el else None
        output.append({
            "index": idx,
            "caption": caption,
            "with_span": extract_table_with_span_lxml(table)
        })
    return output

def process_tables_with_bs4(html_content):
    soup = BeautifulSoup(html_content, "html.parser")
    tables = soup.find_all("table")
    output = []
    for idx, table in enumerate(tables, 1):
        caption = table.caption.get_text(strip=True) if table.caption else None
        output.append({
            "index": idx,
            "caption": caption,
            "with_span": extract_table_with_span_bs4(table)
        })
    return output

def process_tables(html_content):
    try:
        return process_tables_with_lxml(html_content)
    except (etree.XMLSyntaxError, etree.ParserError, ValueError) as e:
        print(f"⚠️ Malformed HTML detected. Falling back to BeautifulSoup. Error: {e}")
        return process_tables_with_bs4(html_content)

def main():
    input_file = "trial1.txt"
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    with open(input_file, "r", encoding="utf-8") as f:
        html_content = f.read()

    tables = process_tables(html_content)

    for table in tables:
        filepath = os.path.join(output_dir, f"table_{table['index']}.json")
        with open(filepath, "w", encoding="utf-8") as out_file:
            json.dump(table, out_file, indent=2, ensure_ascii=False)

    print(f"✅ Saved {len(tables)} tables to '{output_dir}/'")

if __name__ == "__main__":
    main()
