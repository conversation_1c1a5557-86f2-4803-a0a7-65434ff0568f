#!/usr/bin/env python3
import shutil
import os

def remove_folder(folder_name):
    if os.path.exists(folder_name) and os.path.isdir(folder_name):
        try:
            shutil.rmtree(folder_name)
            print(f"Removed folder '{folder_name}' and its contents.")
        except Exception as e:
            print(f"Error removing '{folder_name}': {e}")
    else:
        print(f"Folder '{folder_name}' does not exist.")

if __name__ == "__main__":
    remove_folder("output")
    remove_folder("output2")
