#!/usr/bin/env python3
"""
Comprehensive Table Processing Test Script - Enhanced Testing Suite

This script provides comprehensive testing of the enhanced table processing system
using code_version_2.py with all improvements and fixes applied.

=== TEST SCOPE AND COVERAGE ===

Files Tested:
- di_parser_output.txt: Original comprehensive dataset with diverse table structures
- test/*.txt files: All trial files automatically discovered (trial1.txt through trial28.txt)
- Covers 100+ different table patterns and edge cases

FUNCTION TESTED: correct_tables() from code_version_2.py (Enhanced Version)
OUTPUT: Complete results saved to 'comprehensive_test_output.txt'

=== ENHANCED FEATURES TESTED ===

Fixed Issues Validation:
- Column disappearing fix: Tests tables with colspan headers that previously lost columns
- Enhanced classification: Validates improved _is_title_plus_headers_pattern() logic
- UTF-16/UTF-8 BOM support: Tests international symbols and encoding handling
- Header structure analysis: Verifies correct identification of spanning header patterns

Core Table Processing Features:
- All table structure types: Simple grids, complex spanning, hierarchical nested
- Advanced header patterns: Multiple header rows, spanning elements, mixed structures
- Data preservation: Empty cells, columns, and structure maintenance
- Symbol processing: Unicode symbol replacement with enhanced encoding support
- Column suffix handling: _2, _3, _4 pattern merging with comma separation
- Caption preservation: Maintains table captions and generates title tags
- Error resilience: Individual table processing with comprehensive error isolation

Classification System Testing:
- "multiple_th_header_rows_with_colspan": FIXED classification prevents column loss
- "hierarchical_headers": Complex nested structures with 3+ header levels
- "alignment_based": High empty ratio tables for visual alignment
- "complex_header_spanning": Multi-level headers with colspan/rowspan
- "filled_colspan_header_spanning": Dense data tables with spanning headers
- "grouped_header": Column headers grouped by category
- "simple": Standard tabular data structures
- Edge case handling: Malformed tables and unusual structures

Processing Workflow Validation:
- Individual table processing: Each table handled independently
- Dual parser fallback: lxml primary with BeautifulSoup backup
- Memory efficiency: In-memory processing without temporary files
- Structure preservation: Original HTML layout maintained for non-table content
- Encoding support: UTF-16/UTF-8 BOM detection and handling

=== OUTPUT ANALYSIS ===

Test Results Organization:
- Results organized by source file for systematic analysis
- Complete JSON conversion outputs preserved for verification
- Processing statistics include table counts and classification distribution
- Error reporting for any failed conversions with detailed diagnostics
- Performance metrics and processing time analysis

Validation Criteria:
- No column loss in complex spanning header tables (CRITICAL FIX)
- Proper JSON structure selection based on table classification
- Complete symbol replacement with UTF-16/UTF-8 support
- Caption and title preservation across all table types
- Consistent formatting and structure across all conversions

=== SPECIFIC TEST CASES FOR FIXES ===

Column Disappearing Fix Validation:
The test suite specifically validates the fix for tables like:
```html
<table>
<tr><th colspan="2">Product Information</th><th>Price</th><th>Stock</th></tr>
<tr><th>Name</th><th>Category</th><th>USD</th><th>Units</th></tr>
<tr><td>Apple</td><td>Fruit</td><td>$1.00</td><td>100</td></tr>
</table>
```

BEFORE FIX (Column Loss):
- Classification: "hierarchical_headers" (WRONG)
- Result: {"Product Information":"Category","Price":"USD","Stock":"Units",...}
- ISSUE: "Name" column completely disappeared

AFTER FIX (All Columns Preserved):
- Classification: "multiple_th_header_rows_with_colspan" (CORRECT)
- Result: [{"Name":"Apple","Category":"Fruit","USD":"$1.00","Units":"100"}]
- SUCCESS: All columns preserved (Name, Category, USD, Units)

Test Files Containing This Pattern:
- Tables with mixed spanning/non-spanning headers in first row
- Multiple header row structures with colspan elements
- Complex business tables with product/category hierarchies
- Financial tables with grouped column headers

UTF-16/UTF-8 BOM Symbol Testing:
- International symbols: ☑✓❌◻⬜ and extended Unicode sets
- Encoding detection and fallback mechanisms
- Symbol replacement accuracy across different encodings
- BOM handling for files with byte order marks

Classification Accuracy Testing:
- Verification that all table types are correctly identified
- No misclassification of spanning header tables
- Proper differentiation between hierarchical and multi-level patterns
- Edge case handling for unusual table structures
"""

import code_version_2
import os

print('Testing all files...')
print()

# Open output file for writing all results
with open('comprehensive_test_output.txt', 'w', encoding='utf-8') as output_file:
    output_file.write('=== COMPREHENSIVE TABLE CONVERSION OUTPUTS ===\n\n')

    # Get all files in test folder automatically
    trial_files = []
    if os.path.exists('test'):
        for filename in os.listdir('test'):
            if filename.endswith('.txt'):
                trial_files.append(f'test/{filename}')

    # Sort files numerically (trial1, trial2, trial3... not trial1, trial10, trial11...)
    def natural_sort_key(filename):
        import re
        # Extract numbers from filename for proper numerical sorting
        numbers = re.findall(r'\d+', filename)
        if numbers:
            return (filename.split('trial')[0], int(numbers[0]) if numbers else 0, filename)
        return (filename, 0, filename)

    trial_files.sort(key=natural_sort_key)

    print(f'Found {len(trial_files)} files in test folder')
    print()

    total_tables = 0
    total_json = 0
    total_html = 0
    files_with_empty_preservation = 0

    # Process di_parser_output.txt first if it exists
    if os.path.exists('di_parser_output.txt'):
        try:
            print('Processing di_parser_output.txt...')
            content = open('di_parser_output.txt', 'r', encoding='utf-8').read()
            result = code_version_2.correct_tables(content)

            # Write to output file
            output_file.write('=== DI_PARSER_OUTPUT.TXT ===\n')
            output_file.write(result)
            output_file.write('\n\n')

            # Count tables for statistics
            tables = []
            start = 0
            while True:
                table_start = result.find('<table>', start)
                if table_start == -1:
                    break
                table_end = result.find('</table>', table_start) + 8
                tables.append(result[table_start:table_end])
                start = table_end

            json_count = 0
            html_count = 0

            for table in tables:
                # Check for JSON patterns: arrays with objects, single objects, or empty arrays
                if (('[{' in table and '}]' in table) or
                    ('{' in table and '}' in table and not '<tr>' in table) or
                    ('[]' in table and not '<tr>' in table)):
                    json_count += 1
                else:
                    html_count += 1

            total_tables += len(tables)
            total_json += json_count
            total_html += html_count

            success_rate = 100 * json_count / len(tables) if tables else 0
            empty_preserved = '""' in result or '": ""' in result
            if empty_preserved:
                files_with_empty_preservation += 1

            status = '✅' if json_count == len(tables) else '❌'
            print(f'{status} di_parser_output.txt: {len(tables)} tables, {json_count} JSON ({success_rate:.1f}%)')

        except Exception as e:
            print(f'❌ Error processing di_parser_output.txt: {e}')
            output_file.write(f'=== DI_PARSER_OUTPUT.TXT ===\nError: {e}\n\n')

    # Process all trial files
    for trial_file in trial_files:
        try:
            print(f'Processing {trial_file}...')
            content = open(trial_file, 'r', encoding='utf-8').read()
            result = code_version_2.correct_tables(content)

            # Write to output file
            output_file.write(f'=== {trial_file.upper()} ===\n')
            output_file.write(result)
            output_file.write('\n\n')

            # Count tables for statistics
            tables = []
            start = 0
            while True:
                table_start = result.find('<table>', start)
                if table_start == -1:
                    break
                table_end = result.find('</table>', table_start) + 8
                tables.append(result[table_start:table_end])
                start = table_end

            json_count = 0
            html_count = 0

            for table in tables:
                # Check for JSON patterns: arrays with objects, single objects, or empty arrays
                if (('[{' in table and '}]' in table) or
                    ('{' in table and '}' in table and not '<tr>' in table) or
                    ('[]' in table and not '<tr>' in table)):
                    json_count += 1
                else:
                    html_count += 1

            total_tables += len(tables)
            total_json += json_count
            total_html += html_count

            success_rate = 100 * json_count / len(tables) if tables else 0

            # Check if empty values are preserved
            empty_preserved = '""' in result or '": ""' in result
            if empty_preserved:
                files_with_empty_preservation += 1

            status = '✅' if json_count == len(tables) else '❌'
            print(f'{status} {trial_file}: {len(tables)} tables, {json_count} JSON ({success_rate:.1f}%)')

        except Exception as e:
            print(f'❌ Error processing {trial_file}: {e}')
            output_file.write(f'=== {trial_file.upper()} ===\nError: {e}\n\n')

print()
print('🎯 COMPREHENSIVE RESULTS:')
total_files = len(trial_files) + (1 if os.path.exists('di_parser_output.txt') else 0)
print(f'   📊 Total files tested: {total_files}')
print(f'   📊 Total tables processed: {total_tables}')
print(f'   ✅ Successfully converted to JSON: {total_json} ({100*total_json/total_tables:.1f}%)')
print(f'   ❌ Still in HTML format: {total_html}')
print()

if total_json == total_tables:
    print('✅ ALL TABLES CONVERTED SUCCESSFULLY')
else:
    print('❌ Some tables failed to convert')

print()
print('🏆 FINAL RESULTS:')
print(f'   📊 Grand total tables: {total_tables}')
print(f'   ✅ JSON converted: {total_json} ({100*total_json/total_tables:.1f}%)')
print(f'   ❌ HTML format: {total_html}')
print()
print(f'📄 All outputs saved to: comprehensive_test_output.txt')
