# POTENTIAL GENERALIZED NAMES FOR FILES AND FUNCTIONS

## TEST FOLDER FILES (trial_<feature>_<complexity>.txt)

### Current Files → Suggested Names
test_symbol.txt → trial_symbol_detection_simple.txt
trial1.txt → trial_mixed_tables_complex.txt
trial2.txt → trial_hierarchical_headers_medium.txt
trial3.txt → trial_spanning_columns_medium.txt
trial4.txt → trial_financial_data_simple.txt
trial5.txt → trial_course_schedule_medium.txt
trial6.txt → trial_product_catalog_complex.txt
trial7.txt → trial_employee_records_medium.txt
trial8.txt → trial_sales_reports_complex.txt
trial9.txt → trial_inventory_tracking_medium.txt
trial10.txt → trial_alignment_based_complex.txt
trial11.txt → trial_function_subfunction_medium.txt
trial12.txt → trial_grouped_headers_simple.txt
trial13.txt → trial_multi_level_headers_medium.txt
trial14.txt → trial_rowspan_patterns_complex.txt
trial15.txt → trial_colspan_spanning_medium.txt
trial16.txt → trial_nested_structures_complex.txt
trial17.txt → trial_data_heavy_tables_medium.txt
trial18.txt → trial_sparse_layouts_medium.txt
trial19.txt → trial_temporal_data_simple.txt
trial20.txt → trial_performance_metrics_medium.txt
trial21.txt → trial_filled_colspan_headers_complex.txt
trial22.txt → trial_dashboard_layouts_complex.txt
trial23.txt → trial_comparison_tables_simple.txt
trial24.txt → trial_statistical_data_medium.txt
trial25.txt → trial_simple_two_level_headers_simple.txt
trial26.txt → trial_budget_allocation_medium.txt
trial27.txt → trial_survey_results_medium.txt
trial28.txt → trial_production_schedules_complex.txt

## CODE_VERSIONS FOLDER FILES (code_ver_<feature_added>.py)

### Current Files → Suggested Names
code_ver1.py → code_ver_basic_extraction.py
code_ver2.py → code_ver_symbol_detection.py
code_ver3.py → code_ver_classification_system.py
code_ver4.py → code_ver_header_analysis.py
code_ver5.py → code_ver_spanning_support.py
code_ver6.py → code_ver_hierarchical_processing.py
code_ver7.py → code_ver_empty_cell_handling.py
code_ver8.py → code_ver_column_preservation.py
code_ver9.py → code_ver_error_handling.py
code_ver10.py → code_ver_dual_parser_fallback.py
code_ver11.py → code_ver_utf16_encoding_support.py
code_ver12.py → code_ver_enhanced_symbol_mapping.py
code_ver13.py → code_ver_caption_preservation.py
code_ver14.py → code_ver_suffix_column_combining.py
code_ver15.py → code_ver_pattern_recognition.py
code_ver16.py → code_ver_feature_extraction.py
code_ver17.py → code_ver_dynamic_classification.py
code_ver18.py → code_ver_matrix_building.py
code_ver19.py → code_ver_conversion_strategies.py
code_ver20.py → code_ver_json_optimization.py
code_ver21.py → code_ver_comprehensive_testing.py
code_ver22.py → code_ver_performance_improvements.py
code_ver23.py → code_ver_robustness_enhancements.py
code_ver24.py → code_ver_compatibility_fixes.py
code_ver25.py → code_ver_production_ready.py
code_ver26.py → code_ver_adaptive_thresholds.py
code_ver27.py → code_ver_dataset_analysis.py

## FUNCTION NAMES IN CODE_VERSION_2.PY

### Core Processing Functions
correct_tables() → process_document_tables()
process_tables() → extract_and_analyze_tables()
classify_table_from_data() → determine_table_structure_type()
convert_to_key_value_json() → transform_table_to_json()

### Table Extraction Functions
process_tables_with_lxml() → extract_tables_primary_parser()
process_tables_with_bs4() → extract_tables_fallback_parser()
extract_table_with_span_lxml() → parse_spanning_table_lxml()
extract_table_with_span_bs4() → parse_spanning_table_bs4()

### Analysis Functions
extract_table_features() → analyze_table_characteristics()
build_expanded_matrix() → create_normalized_table_matrix()
classify_by_structure() → categorize_by_structural_patterns()
analyze_header_structure() → examine_header_hierarchy()

### Adaptive Threshold Functions
_analyze_dataset_statistics() → compute_dataset_metrics()
_compute_adaptive_thresholds() → calculate_dynamic_thresholds()
_get_or_compute_dataset_stats() → retrieve_or_generate_dataset_analysis()
_calculate_dynamic_thresholds() → apply_adaptive_classification_rules()

### Conversion Functions
convert_simple_table() → transform_basic_table_structure()
convert_complex_header_spanning_table() → process_multi_level_header_table()
convert_alignment_based_table() → handle_sparse_alignment_table()
convert_hierarchical_two_level_table() → process_nested_hierarchy_table()
convert_grouped_header_table() → transform_categorized_header_table()

### Symbol Processing Functions
detect_unicode_symbols() → identify_special_characters()
get_replacement_for_symbol() → map_symbol_to_text()
replace_symbols() → substitute_unicode_symbols()
process_json_data_with_symbol_replacement() → apply_symbol_transformation()

### Utility Functions
combine_suffix_columns() → merge_related_columns()
clean_footnote_references() → remove_reference_markers()
load_table_data() → read_table_from_file()

## VARIABLE NAMES IN CODE_VERSION_2.PY

### Configuration Variables
TABLE_CLASSIFICATION_CONFIG → table_processing_configuration
POSITIVE_SYMBOLS → affirmative_unicode_symbols
NEGATIVE_SYMBOLS → negative_unicode_symbols
NEUTRAL_SYMBOLS → neutral_unicode_symbols
UNICODE_CODE_MAPPINGS → symbol_replacement_mappings

### Cache Variables
_DATASET_STATS_CACHE → dataset_analysis_cache

### Feature Variables
total_rows → table_row_count
total_cells → table_cell_count
empty_ratio → empty_cell_percentage
header_ratio → header_cell_percentage
colspan_cells → column_spanning_cells
rowspan_cells → row_spanning_cells
year_indicators → temporal_data_markers

### Threshold Variables
min_rows_small → small_table_minimum_rows
max_rows_small → small_table_maximum_rows
low_empty_ratio → sparse_table_threshold
high_empty_ratio → alignment_table_threshold
min_year_indicators → temporal_detection_minimum

### Processing Variables
matrix → normalized_table_grid
features → table_characteristics
classification → structure_category
thresholds → classification_parameters
percentiles → statistical_distribution_data

## NAMING CONVENTIONS APPLIED

### Files:
- test/: trial_<feature>_<complexity>.txt
- code_versions/: code_ver_<new_feature>.py

### Functions:
- Use descriptive verbs: process_, analyze_, transform_, calculate_
- Include object type: table_, dataset_, symbol_, header_
- Indicate purpose: _extraction, _analysis, _transformation

### Variables:
- Use full descriptive names instead of abbreviations
- Include units/type: _count, _ratio, _percentage, _threshold
- Group related variables with common prefixes

### Constants:
- Use UPPER_CASE for configuration constants
- Include purpose in name: _SYMBOLS, _MAPPINGS, _CONFIG
- Avoid generic names like DATA, INFO, TEMP
