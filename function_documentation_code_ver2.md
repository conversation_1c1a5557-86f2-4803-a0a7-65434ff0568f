# Table Processing System - Complete Function Documentation (Enhanced Version)

## Overview

This document provides comprehensive documentation for the enhanced table processing and conversion system implemented in `finalcode2.py`. The system converts HTML tables with complex structures (colspan, rowspan, hierarchical headers) to JSON format with critical fixes for column disappearing issues and enhanced UTF-16/UTF-8 BOM support.

## Version Information

**This documentation covers finalcode2.py - ENHANCED VERSION with fixes:**
- ✅ Fixed column disappearing issue in tables with spanning headers
- ✅ Enhanced _is_title_plus_headers_pattern() classification logic
- ✅ Improved UTF-16/UTF-8 BOM encoding support for symbols
- ✅ Better error handling and fallback strategies

## Core Architecture

### Enhanced Main Processing Flow
```
HTML Input → correct_tables() → process_tables() → classify_table_from_data() → convert_to_key_value_json() → combine_suffix_columns() → JSON Output
     ↓              ↓                    ↓                      ↓                           ↓                        ↓
Encoding      Dual Parser        Enhanced Header         Fixed Classification      Enhanced Symbol         Comma-separated
Detection     (lxml/BS4)         Analysis               (No Column Loss)          Processing              Suffix Combining
```

### Individual Table Processing Workflow
```
1. Extract table HTML using regex pattern matching
2. Parse table structure using process_tables() with lxml/BeautifulSoup fallback
3. Build expanded matrix from colspan/rowspan data using build_expanded_matrix()
4. Analyze header structure using analyze_header_structure() [ENHANCED]
5. Classify table type using classify_table_from_data() [FIXED]
6. Convert to JSON using convert_to_key_value_json() with appropriate strategy
7. Apply symbol replacement using process_json_data_with_symbol_replacement() [ENHANCED]
8. Combine suffix columns using combine_suffix_columns() if present
9. Replace original table with JSON in HTML string
10. Continue to next table (individual processing ensures error isolation)
```

## Function Categories

### 1. Core Processing Functions

#### `correct_tables(input_string: str) → str`
**ENHANCED: Main entry point** for table processing with fixes for column disappearing issues.

**Purpose**: Primary function that orchestrates the complete table processing workflow with enhanced error handling and improved classification logic.

**Input**: 
- `input_string`: HTML string containing table elements, or nested dictionary with 'content' key
- Supports UTF-16/UTF-8 BOM encoding detection
- Handles mixed content (tables embedded in other HTML)

**Output**: 
- Processed HTML string with tables converted to JSON format
- All non-table content preserved exactly as-is
- Enhanced caption and title tag handling

**Enhanced Features**:
- Fixed classification prevents column loss in spanning header tables
- Improved UTF-16/UTF-8 BOM encoding support
- Better error isolation for individual table processing
- Enhanced symbol processing with fallback mechanisms

**Processing Steps**:
1. Extract content from nested dictionary if needed
2. Detect and handle encoding issues (UTF-16/UTF-8 BOM)
3. Find tables using regex pattern matching
4. Process each table individually with enhanced classification
5. Apply symbol replacement and suffix column combining
6. Save results to 'all_tables_converted.txt'

**Input Example**:
```python
html_input = '''
<table>
<tr><th colspan="2">Product Information</th><th>Price</th></tr>
<tr><th>Name</th><th>Category</th><th>USD</th></tr>
<tr><td>Apple</td><td>Fruit</td><td>$1.00</td></tr>
</table>
'''
```

**Output Example**:
```python
# FIXED: All columns preserved (Name, Category, USD)
'<table><title>Product Information</title>[{"Name":"Apple","Category":"Fruit","USD":"$1.00"}]</table>'
```

**Critical Fix Applied**:
- Previously would lose "Name" column due to misclassification
- Now correctly preserves all columns from both header rows

#### `process_tables(html_content: str) → List[Dict[str, Any]]`
**Table extraction** with enhanced fallback parsing strategy.

**Purpose**: Extracts table structures from HTML content using dual parser approach with improved error handling.

**Input**: 
- `html_content`: HTML string containing one or more table elements
- Supports malformed HTML with graceful degradation

**Output**: 
- List of table dictionaries with complete metadata and structure information
- Each table dict contains: 'with_span', 'caption', 'start_pos', 'end_pos'

**Enhanced Features**:
- Primary lxml parser with BeautifulSoup fallback
- Better handling of malformed HTML structures
- Enhanced caption extraction and position tracking
- Improved colspan/rowspan attribute handling

**Processing Flow**:
1. Try lxml parser first (faster, more accurate)
2. Fall back to BeautifulSoup if lxml fails
3. Extract table structure with span information
4. Capture captions and position data
5. Return structured table data for classification

**Input Example**:
```python
html_content = '''
<table><caption>Sales Data</caption>
<tr><th>Region</th><th>Q1</th></tr>
<tr><td>North</td><td>100</td></tr>
</table>
'''
```

**Output Example**:
```python
[{
    'with_span': [
        [{'tag': 'th', 'text': 'Region', 'rowspan': 1, 'colspan': 1},
         {'tag': 'th', 'text': 'Q1', 'rowspan': 1, 'colspan': 1}],
        [{'tag': 'td', 'text': 'North', 'rowspan': 1, 'colspan': 1},
         {'tag': 'td', 'text': '100', 'rowspan': 1, 'colspan': 1}]
    ],
    'caption': 'Sales Data',
    'start_pos': 0,
    'end_pos': 120
}]
```

#### `classify_table_from_data(table_data: Dict[str, Any]) → str`
**ENHANCED: Structure classification** with fixed header pattern recognition.

**Purpose**: Analyzes table structure patterns to determine optimal conversion strategy with improved classification logic to prevent column loss.

**Input**: 
- `table_data`: Table data dictionary with 'with_span' structure from process_tables()

**Output**: 
- Classification string determining conversion strategy
- Enhanced classifications include fixed spanning header detection

**Critical Enhancement**:
- Fixed _is_title_plus_headers_pattern() logic prevents column disappearing
- Better differentiation between hierarchical and multi-level patterns
- Improved header structure analysis

**Classification Types** (Enhanced):
- `"multiple_th_header_rows_with_colspan"`: FIXED - Two header rows with spanning elements
- `"hierarchical_headers"`: Complex nested structures (3+ header levels)
- `"multi_level_headers"`: Tables with 2+ header rows using hierarchical naming
- `"alignment_based"`: High empty ratio tables for visual alignment
- `"complex_header_spanning"`: Multi-level headers with colspan/rowspan
- `"simple"`: Standard tabular data
- `"other"`: Fallback for edge cases

**Processing Flow**:
1. Analyze header structure using analyze_header_structure()
2. Check for specific patterns (title+headers, hierarchical, multi-level)
3. Extract structural features using extract_table_features()
4. Apply enhanced classification rules
5. Return appropriate classification for conversion

**Input Example**:
```python
table_data = {
    'with_span': [
        [{'tag': 'th', 'text': 'Product Info', 'colspan': 2}, {'tag': 'th', 'text': 'Price'}],
        [{'tag': 'th', 'text': 'Name'}, {'tag': 'th', 'text': 'Category'}, {'tag': 'th', 'text': 'USD'}],
        [{'tag': 'td', 'text': 'Apple'}, {'tag': 'td', 'text': 'Fruit'}, {'tag': 'td', 'text': '$1.00'}]
    ]
}
```

**Output Example**:
```python
"multiple_th_header_rows_with_colspan"  # FIXED: Correctly identified, prevents column loss
```

#### `convert_to_key_value_json(table: Dict[str, Any], classification: str) → Union[Dict, List]`
**Enhanced conversion dispatcher** with improved strategy selection.

**Purpose**: Routes tables to appropriate conversion functions based on enhanced classification with better handling of complex structures.

**Input**: 
- `table`: Table data dictionary with structure information
- `classification`: Enhanced classification string from classify_table_from_data()

**Output**: 
- JSON structure (Dict or List) optimized for the specific table type
- Enhanced preservation of all columns and data

**Enhanced Features**:
- Better routing based on fixed classification
- Improved matrix expansion with build_expanded_matrix()
- Enhanced handling of edge cases and malformed structures
- Better preservation of empty cells and columns

**Conversion Strategy Mapping**:
```python
{
    "multiple_th_header_rows_with_colspan": convert_multiple_th_header_rows_with_colspan_table,
    "hierarchical_headers": convert_hierarchical_headers_table,
    "multi_level_headers": convert_multi_level_headers_table,
    "alignment_based": convert_alignment_based_table,
    "complex_header_spanning": convert_complex_header_spanning_table,
    "simple": convert_simple_table,
    "other": convert_simple_table
}
```

**Processing Flow**:
1. Build expanded matrix from table data
2. Check for special patterns (header-only, single-row)
3. Route to appropriate conversion function based on classification
4. Apply post-processing for data cleanup
5. Return structured JSON data

**Input Example**:
```python
table = {'with_span': [...]}  # Table with spanning headers
classification = "multiple_th_header_rows_with_colspan"
```

**Output Example**:
```python
[{"Name": "Apple", "Category": "Fruit", "USD": "$1.00"}]  # All columns preserved
```

#### `combine_suffix_columns(data: Any) → Any`
**Enhanced column suffix combining** for _2, _3, _4 patterns with comma separation.

**Purpose**: Post-processes JSON data to combine columns with numeric suffixes (_2, _3, _4) into their base columns using comma separation.

**Input**:
- `data`: JSON data structure (dict, list, or other types)
- Handles nested structures recursively

**Output**:
- Modified data structure with suffix columns combined
- Uses comma separation for multiple values
- Preserves structure type and hierarchy

**Enhanced Features**:
- Recursive processing of nested structures
- Intelligent handling of empty values
- Comma separation as specified in requirements
- Better handling of orphaned suffix columns

**Processing Logic**:
1. Identify base columns and suffix columns (_2, _3, _4, etc.)
2. Group related columns by base name
3. Combine non-empty values with comma separation
4. Remove suffix columns after successful combination
5. Handle nested structures recursively

**Input Example**:
```python
data = {
    "Name": "John",
    "Name_2": "Smith",
    "Age": "25",
    "Details": "",
    "Details_2": "Manager"
}
```

**Output Example**:
```python
{
    "Name": "John,Smith",
    "Age": "25",
    "Details": "Manager"
}
```

### 2. Enhanced Table Extraction Functions

#### `process_tables_with_lxml(html_content: str) → List[Dict[str, Any]]`
**Primary table extraction** using lxml parser with enhanced error handling.

**Purpose**: Fast and accurate table extraction for well-formed HTML using lxml parser.

**Input**:
- `html_content`: HTML string with table elements

**Output**:
- List of table dictionaries with complete structure information
- Enhanced metadata extraction

**Enhanced Features**:
- Better handling of malformed HTML
- Improved colspan/rowspan attribute extraction
- Enhanced caption and position tracking
- More robust error handling

**Processing Flow**:
1. Parse HTML using lxml.html
2. Find all table elements
3. Extract table structure with span information
4. Capture captions and metadata
5. Return structured data for classification

#### `process_tables_with_bs4(html_content: str) → List[Dict[str, Any]]`
**Fallback table extraction** using BeautifulSoup with enhanced compatibility.

**Purpose**: Robust fallback parser for malformed HTML when lxml fails.

**Input**:
- `html_content`: HTML string (potentially malformed)

**Output**:
- List of table dictionaries with structure information
- More tolerant of HTML errors

**Enhanced Features**:
- Better handling of malformed HTML structures
- Improved attribute extraction
- Enhanced error recovery
- More robust parsing of edge cases

#### `extract_table_with_span_lxml(table_el) → List[List[Dict]]`
**Enhanced lxml span extraction** with improved attribute handling.

**Purpose**: Extracts detailed table structure including rowspan/colspan information using lxml.

**Input**:
- `table_el`: lxml table element

**Output**:
- 2D list of cell dictionaries with span information
- Each cell contains: tag, text, rowspan, colspan

**Enhanced Features**:
- Better handling of missing attributes
- Improved text extraction and cleaning
- Enhanced span attribute processing
- More robust error handling

#### `extract_table_with_span_bs4(table) → List[List[Dict]]`
**Enhanced BeautifulSoup span extraction** with improved compatibility.

**Purpose**: Extracts table structure using BeautifulSoup for malformed HTML.

**Input**:
- `table`: BeautifulSoup table element

**Output**:
- 2D list of cell dictionaries with span information
- Enhanced error tolerance

**Enhanced Features**:
- Better handling of malformed HTML
- Improved attribute extraction
- Enhanced text cleaning
- More robust error recovery

### 3. Enhanced Structure Analysis Functions

#### `extract_table_features(table: Dict[str, Any]) → Dict[str, Any]`
**Comprehensive feature extraction** for enhanced classification.

**Purpose**: Analyzes table structure to extract features used for classification with improved accuracy.

**Input**:
- `table`: Table data dictionary with 'with_span' structure

**Output**:
- Dictionary of structural features for classification
- Enhanced feature set for better pattern recognition

**Features Extracted**:
- `total_rows`: Number of rows in table
- `total_cells`: Total number of cells
- `rowspan_cells`: Count of cells with rowspan > 1
- `colspan_cells`: Count of cells with colspan > 1
- `empty_cells`: Count of empty cells
- `empty_ratio`: Percentage of empty cells
- `header_cells`: Count of th elements
- `numeric_cells`: Count of cells containing numbers
- `year_indicators`: Count of cells with 4-digit years
- `column_consistency`: Whether rows have consistent column counts

**Enhanced Analysis**:
- Better detection of spanning patterns
- Improved empty cell ratio calculation
- Enhanced header structure analysis
- More accurate numeric content detection

**Input Example**:
```python
table = {
    'with_span': [
        [{'tag': 'th', 'text': 'Name', 'colspan': 1}, {'tag': 'th', 'text': 'Age', 'colspan': 1}],
        [{'tag': 'td', 'text': 'John', 'colspan': 1}, {'tag': 'td', 'text': '25', 'colspan': 1}]
    ]
}
```

**Output Example**:
```python
{
    'total_rows': 2,
    'total_cells': 4,
    'rowspan_cells': 0,
    'colspan_cells': 0,
    'empty_cells': 0,
    'empty_ratio': 0.0,
    'header_cells': 2,
    'numeric_cells': 1,
    'year_indicators': 0,
    'column_consistency': True
}
```

#### `build_expanded_matrix(table: Dict[str, Any]) → List[List[str]]`
**Enhanced matrix expansion** with improved span handling.

**Purpose**: Converts table structure with rowspan/colspan into expanded 2D matrix for easier processing.

**Input**:
- `table`: Table data dictionary with span information

**Output**:
- 2D list representing expanded table matrix
- All spans resolved into individual cells

**Enhanced Features**:
- Better handling of complex span combinations
- Improved matrix dimension calculation
- Enhanced error handling for malformed spans
- More robust cell placement logic

**Processing Logic**:
1. Calculate actual matrix dimensions considering spans
2. Initialize empty matrix with proper dimensions
3. Fill matrix cells considering rowspan/colspan
4. Handle overlapping spans and edge cases
5. Return complete expanded matrix

**Input Example**:
```python
table = {
    'with_span': [
        [{'text': 'Header', 'colspan': 2}],
        [{'text': 'A'}, {'text': 'B'}]
    ]
}
```

**Output Example**:
```python
[
    ['Header', 'Header'],
    ['A', 'B']
]
```

#### `analyze_header_structure(table: Dict[str, Any]) → Dict[str, Any]`
**ENHANCED: Header structure analysis** with fixed classification logic.

**Purpose**: Analyzes header patterns to determine processing strategy with improved accuracy to prevent column loss.

**Input**:
- `table`: Table data dictionary with 'with_span' structure

**Output**:
- Dictionary with header analysis results and processing recommendations

**Critical Enhancement**:
- Fixed _is_title_plus_headers_pattern() logic
- Better detection of spanning header relationships
- Improved strategy recommendations

**Analysis Results**:
- `th_row_count`: Number of rows containing th elements
- `th_rows`: List of (row_index, row_data) tuples for th rows
- `header_strategy`: Recommended processing strategy
- `has_hierarchy`: Whether table has hierarchical structure
- `total_columns`: Total columns in expanded matrix

**Header Strategies**:
- `'title_plus_headers'`: Two header rows with spanning elements (FIXED)
- `'hierarchical'`: Complex nested structures (3+ levels)
- `'multi_level'`: Multiple header rows with simpler structure
- `'single_header'`: Single header row
- `'none'`: No header structure detected

**Input Example**:
```python
table = {
    'with_span': [
        [{'tag': 'th', 'text': 'Product Info', 'colspan': 2}],
        [{'tag': 'th', 'text': 'Name'}, {'tag': 'th', 'text': 'Category'}]
    ]
}
```

**Output Example**:
```python
{
    'th_row_count': 2,
    'th_rows': [(0, [...]), (1, [...])],
    'header_strategy': 'title_plus_headers',  # FIXED: Correctly identified
    'has_hierarchy': False,
    'total_columns': 2
}
```

### 4. Enhanced Conversion Functions by Table Type

#### `convert_multiple_th_header_rows_with_colspan_table(matrix: List[List[str]]) → List[Dict[str, str]]`
**CRITICAL FIX: Multiple header rows with spanning** - prevents column disappearing.

**Purpose**: Handles tables with two header rows where first row has spanning elements. This function contains the critical fix for column disappearing issues.

**Input**:
- `matrix`: Expanded 2D matrix from build_expanded_matrix()

**Output**:
- List of dictionaries with all columns preserved
- Uses second header row as column names

**Critical Fix Applied**:
- Previously columns from second header row would disappear
- Now correctly uses Row 1 as column headers
- Preserves all columns from both header rows

**Processing Logic**:
1. Identify Row 0 as title header (with spanning)
2. Use Row 1 as actual column headers (FIXED)
3. Process data rows starting from Row 2
4. Preserve all columns without loss

**Input Example**:
```python
matrix = [
    ['Product Information', 'Product Information', 'Price'],
    ['Name', 'Category', 'USD'],
    ['Apple', 'Fruit', '$1.00']
]
```

**Output Example**:
```python
[{"Name": "Apple", "Category": "Fruit", "USD": "$1.00"}]  # All columns preserved
```

#### `convert_hierarchical_headers_table(matrix: List[List[str]]) → Dict[str, Any]`
**Enhanced hierarchical structure** conversion with improved nesting.

**Purpose**: Handles tables with 3+ header levels creating nested JSON structures.

**Input**:
- `matrix`: Expanded matrix with hierarchical headers

**Output**:
- Nested dictionary structure reflecting hierarchy
- Enhanced preservation of relationships

**Enhanced Features**:
- Better detection of header vs data rows
- Improved nesting logic
- Enhanced handling of complex hierarchies

#### `convert_alignment_based_table(matrix: List[List[str]]) → Dict[str, Any]`
**Enhanced alignment-based** conversion for high empty ratio tables.

**Purpose**: Handles tables with many empty cells used for visual alignment.

**Input**:
- `matrix`: Matrix with high empty cell ratio (>40%)

**Output**:
- Nested dictionary structure using data as keys
- Enhanced section detection

**Enhanced Features**:
- Better empty cell handling
- Improved section header detection
- Enhanced data grouping logic

#### `convert_standard_table(matrix: List[List[str]]) → List[Dict[str, str]]`
**Enhanced standard table** conversion with improved column preservation.

**Purpose**: Handles standard tabular data with enhanced empty cell preservation.

**Input**:
- `matrix`: Standard table matrix

**Output**:
- List of dictionaries with enhanced structure preservation
- Better handling of empty columns

**Enhanced Features**:
- Improved empty cell preservation
- Better column structure maintenance
- Enhanced header handling

### 5. Enhanced Symbol Processing Functions

#### `detect_unicode_symbols_enhanced(text: str) → List[Dict[str, str]]`
**ENHANCED: Unicode symbol detection** with UTF-16/UTF-8 BOM support.

**Purpose**: Detects Unicode symbols in text with enhanced encoding support and fallback mechanisms.

**Input**:
- `text`: Input text that may contain Unicode symbols

**Output**:
- List of dictionaries with enhanced symbol information
- Includes UTF-16 compatibility and fallback data

**Enhanced Features**:
- UTF-16/UTF-8 BOM encoding support
- Enhanced Unicode code point mappings
- Better fallback mechanisms for encoding issues
- Improved symbol categorization

**Symbol Information Returned**:
- `symbol`: The actual Unicode character
- `unicode_code`: Unicode code point (U+XXXX format)
- `unicode_name`: Unicode character name
- `category`: Symbol category (positive/negative/neutral)
- `utf16_supported`: Whether UTF-16 encoding is supported
- `utf8_bom_compatible`: BOM compatibility status
- `fallback_available`: Whether fallback mapping exists

**Input Example**:
```python
text = "Task completed ✓ with issues ❌"
```

**Output Example**:
```python
[
    {
        'symbol': '✓',
        'unicode_code': 'U+2713',
        'unicode_name': 'CHECK MARK',
        'category': 'positive',
        'utf16_supported': True,
        'utf8_bom_compatible': True,
        'fallback_available': True
    },
    {
        'symbol': '❌',
        'unicode_code': 'U+274C',
        'unicode_name': 'CROSS MARK',
        'category': 'negative',
        'utf16_supported': True,
        'utf8_bom_compatible': True,
        'fallback_available': True
    }
]
```

#### `replace_symbols_enhanced(text: str) → str`
**ENHANCED: Symbol replacement** with UTF-16/UTF-8 BOM support and fallback mappings.

**Purpose**: Replaces Unicode symbols with text equivalents using enhanced encoding support.

**Input**:
- `text`: Text containing Unicode symbols

**Output**:
- Text with symbols replaced by appropriate text values
- Enhanced encoding handling

**Enhanced Features**:
- UTF-16/UTF-8 BOM encoding detection and handling
- Extended Unicode code point mappings
- Better fallback mechanisms for encoding issues
- Improved symbol replacement accuracy

**Symbol Replacement Mappings**:
- Positive symbols (☑✓✔✅): → "Yes"
- Negative symbols (☐✗✘❌): → "No"
- Neutral symbols (◻⬜—❓): → "Neutral"
- Unknown symbols: → "Unknown"

**Input Example**:
```python
text = "Status: ✓ Complete, Issues: ❌ Found"
```

**Output Example**:
```python
"Status: Yes Complete, Issues: No Found"
```

#### `process_symbols_enhanced(data: Any) → Any`
**ENHANCED: JSON symbol processing** with recursive structure handling.

**Purpose**: Processes JSON data structures to replace Unicode symbols with enhanced encoding support.

**Input**:
- `data`: JSON data structure (dict, list, str, or other)

**Output**:
- Processed data with symbols replaced using enhanced methods
- Maintains structure integrity

**Enhanced Features**:
- Recursive processing of nested structures
- Enhanced encoding handling
- Better preservation of data types
- Improved error handling

**Processing Logic**:
1. Recursively traverse data structures
2. Apply enhanced symbol replacement to string values
3. Process both keys and values in dictionaries
4. Handle nested structures appropriately
5. Preserve non-string data types

### 6. Enhanced Utility Functions

#### `handle_encoding_issues_enhanced(content: str) → str`
**ENHANCED: Encoding issue handling** with UTF-16/UTF-8 BOM support.

**Purpose**: Handles encoding issues in string content with enhanced BOM detection and normalization.

**Input**:
- `content`: String content that may have encoding issues

**Output**:
- Properly decoded and normalized content
- Enhanced encoding compatibility

**Enhanced Features**:
- BOM character removal (UTF-8, UTF-16)
- Unicode normalization (NFC)
- Better error handling
- Improved compatibility across encodings

#### `detect_file_encoding_enhanced(filepath: str) → str`
**ENHANCED: File encoding detection** with comprehensive BOM support.

**Purpose**: Detects file encoding with enhanced BOM pattern recognition.

**Input**:
- `filepath`: Path to file for encoding analysis

**Output**:
- Detected encoding string with BOM support
- Enhanced fallback mechanisms

**Enhanced Features**:
- Comprehensive BOM pattern detection
- Multiple encoding fallback attempts
- Better error handling
- Improved accuracy for international files

**BOM Patterns Supported**:
- UTF-8 BOM: `\xef\xbb\xbf`
- UTF-16 Little Endian: `\xff\xfe`
- UTF-16 Big Endian: `\xfe\xff`
- UTF-32 Little Endian: `\xff\xfe\x00\x00`
- UTF-32 Big Endian: `\x00\x00\xfe\xff`

### 7. Enhanced Classification Helper Functions

#### `_is_title_plus_headers_pattern(th_rows: List, total_columns: int) → bool`
**CRITICAL FIX: Title+headers pattern detection** - prevents column disappearing.

**Purpose**: Detects tables with title headers followed by column headers. Contains the critical fix for column disappearing issues.

**Input**:
- `th_rows`: List of (row_index, row_data) tuples for rows with th elements
- `total_columns`: Total number of columns (unused after fix)

**Output**:
- Boolean indicating if pattern matches title+headers structure

**Critical Fix Applied**:
- **BEFORE**: Required exactly ONE th element in first row (too restrictive)
- **AFTER**: Checks if ANY th element has colspan > 1 (correct logic)
- **RESULT**: Properly identifies tables with mixed spanning/non-spanning headers

**Fixed Logic**:
```python
# OLD (BROKEN): Required exactly one th element
if len(th_cells_in_first_row) != 1:
    return False

# NEW (FIXED): Check if any th element spans multiple columns
has_spanning_header = any(cell.get('colspan', 1) > 1 for cell in th_cells_in_first_row)
return has_spanning_header
```

**Pattern Detected**:
- Exactly 2 consecutive rows with th elements
- First row contains at least one th element with colspan > 1
- Second row contains actual column headers

**Input Example**:
```python
th_rows = [
    (0, [{'tag': 'th', 'text': 'Product Info', 'colspan': 2}, {'tag': 'th', 'text': 'Price'}]),
    (1, [{'tag': 'th', 'text': 'Name'}, {'tag': 'th', 'text': 'Category'}, {'tag': 'th', 'text': 'USD'}])
]
```

**Output Example**:
```python
True  # FIXED: Correctly identifies spanning header pattern
```

#### `_has_hierarchical_structure(th_rows: List, total_columns: int) → bool`
**Enhanced hierarchical detection** with improved pattern recognition.

**Purpose**: Detects hierarchical header structures with enhanced accuracy.

**Input**:
- `th_rows`: List of header row data
- `total_columns`: Total number of columns

**Output**:
- Boolean indicating hierarchical structure presence

**Enhanced Features**:
- Better colspan pattern analysis
- Improved hierarchy detection logic
- Enhanced handling of complex structures

## Complete Processing Flow

### Entire System Flow (Enhanced)

```
INPUT: HTML String with Tables
         ↓
1. correct_tables(input_string)
   ├─ Extract content from nested dict if needed
   ├─ Handle UTF-16/UTF-8 BOM encoding issues [ENHANCED]
   ├─ Find tables using regex pattern matching
   └─ For each table individually:
         ↓
2. process_tables(html_content)
   ├─ Try lxml parser first (primary)
   ├─ Fall back to BeautifulSoup if needed
   ├─ Extract table structure with span info
   └─ Return structured table data
         ↓
3. build_expanded_matrix(table)
   ├─ Calculate matrix dimensions with spans
   ├─ Fill matrix considering rowspan/colspan
   └─ Return 2D expanded matrix
         ↓
4. analyze_header_structure(table) [ENHANCED]
   ├─ Find rows containing th elements
   ├─ Apply FIXED _is_title_plus_headers_pattern() logic
   ├─ Determine header strategy
   └─ Return analysis results
         ↓
5. classify_table_from_data(table_data) [FIXED]
   ├─ Use enhanced header analysis results
   ├─ Apply improved classification rules
   ├─ Prevent misclassification of spanning headers
   └─ Return correct classification
         ↓
6. convert_to_key_value_json(table, classification)
   ├─ Route to appropriate conversion function
   ├─ Apply enhanced conversion strategies
   ├─ Preserve all columns (NO LOSS) [FIXED]
   └─ Return structured JSON data
         ↓
7. process_symbols_enhanced(data) [ENHANCED]
   ├─ Apply UTF-16/UTF-8 BOM symbol processing
   ├─ Use enhanced Unicode mappings
   ├─ Handle encoding fallbacks
   └─ Return data with symbols replaced
         ↓
8. combine_suffix_columns(data)
   ├─ Identify _2, _3, _4 suffix patterns
   ├─ Combine with comma separation
   ├─ Handle nested structures recursively
   └─ Return data with suffixes combined
         ↓
9. Replace original table in HTML string
   ├─ Add caption/title tags as appropriate
   ├─ Use compact JSON formatting
   └─ Preserve all non-table content
         ↓
10. Continue to next table (individual processing)
         ↓
OUTPUT: Enhanced HTML with JSON tables + file save
```

### Critical Fix Flow (Column Disappearing Prevention)

```
BEFORE FIX (Column Loss):
Table with spanning headers
         ↓
_is_title_plus_headers_pattern() [BROKEN]
├─ Required exactly ONE th element (too restrictive)
├─ Failed for mixed spanning/non-spanning headers
└─ Returned False
         ↓
Misclassified as "hierarchical_headers"
         ↓
convert_hierarchical_headers_table()
├─ Required 3+ header rows (table only had 2)
├─ Failed and returned empty dict
└─ RESULT: Columns disappeared

AFTER FIX (All Columns Preserved):
Table with spanning headers
         ↓
_is_title_plus_headers_pattern() [FIXED]
├─ Checks if ANY th element has colspan > 1
├─ Correctly identifies spanning patterns
└─ Returns True
         ↓
Correctly classified as "multiple_th_header_rows_with_colspan"
         ↓
convert_multiple_th_header_rows_with_colspan_table()
├─ Uses Row 1 as column headers
├─ Preserves all columns from both rows
└─ RESULT: All columns preserved
```

## Complete Input/Output Examples

### Example 1: Simple Table (No Issues)

**Input**:
```html
<table>
<tr><th>Name</th><th>Age</th><th>City</th></tr>
<tr><td>John</td><td>25</td><td>NYC</td></tr>
<tr><td>Jane</td><td>30</td><td>LA</td></tr>
</table>
```

**Processing Flow**:
1. `correct_tables()` → finds table
2. `process_tables()` → extracts structure
3. `classify_table_from_data()` → "simple"
4. `convert_to_key_value_json()` → standard conversion
5. `combine_suffix_columns()` → no suffixes found

**Output**:
```html
<table>[{"Name":"John","Age":"25","City":"NYC"},{"Name":"Jane","Age":"30","City":"LA"}]</table>
```

### Example 2: Complex Spanning Headers (CRITICAL FIX)

**Input**:
```html
<table>
<tr><th colspan="2">Product Information</th><th>Price</th><th>Stock</th></tr>
<tr><th>Name</th><th>Category</th><th>USD</th><th>Units</th></tr>
<tr><td>Apple</td><td>Fruit</td><td>$1.00</td><td>100</td></tr>
<tr><td>Banana</td><td>Fruit</td><td>$0.50</td><td>200</td></tr>
</table>
```

**Processing Flow (FIXED)**:
1. `correct_tables()` → finds table
2. `process_tables()` → extracts with span info
3. `build_expanded_matrix()` → creates expanded matrix:
   ```
   [['Product Information', 'Product Information', 'Price', 'Stock'],
    ['Name', 'Category', 'USD', 'Units'],
    ['Apple', 'Fruit', '$1.00', '100'],
    ['Banana', 'Fruit', '$0.50', '200']]
   ```
4. `analyze_header_structure()` → detects 2 th rows
5. `_is_title_plus_headers_pattern()` → **FIXED**: Returns True (detects colspan > 1)
6. `classify_table_from_data()` → **FIXED**: "multiple_th_header_rows_with_colspan"
7. `convert_multiple_th_header_rows_with_colspan_table()` → **PRESERVES ALL COLUMNS**
8. Result: All columns maintained (Name, Category, USD, Units)

**Output (FIXED)**:
```html
<table><title>Product Information</title>[{"Name":"Apple","Category":"Fruit","USD":"$1.00","Units":"100"},{"Name":"Banana","Category":"Fruit","USD":"$0.50","Units":"200"}]</table>
```

**BEFORE FIX (Column Loss)**:
```html
<table><title>Product Information - Price - Stock</title>[{"Product Information":"Category","Price":"USD","Stock":"Units"},{"Product Information":"Fruit","Price":"$1.00","Stock":"100"},{"Product Information":"Fruit","Price":"$0.50","Stock":"200"}]</table>
```
*Note: "Name" column completely disappeared, "Category" misplaced*

### Example 3: Symbol Processing with UTF-16 Support

**Input**:
```html
<table>
<tr><th>Task</th><th>Status</th><th>Priority</th></tr>
<tr><td>Setup</td><td>✓</td><td>High</td></tr>
<tr><td>Testing</td><td>❌</td><td>Medium</td></tr>
<tr><td>Review</td><td>◻</td><td>Low</td></tr>
</table>
```

**Processing Flow (ENHANCED)**:
1. `correct_tables()` → finds table
2. `process_tables()` → extracts structure
3. `classify_table_from_data()` → "simple"
4. `convert_to_key_value_json()` → standard conversion
5. `process_symbols_enhanced()` → **ENHANCED**: UTF-16/UTF-8 BOM support
   - ✓ → "Yes"
   - ❌ → "No"
   - ◻ → "Neutral"

**Output (ENHANCED)**:
```html
<table>[{"Task":"Setup","Status":"Yes","Priority":"High"},{"Task":"Testing","Status":"No","Priority":"Medium"},{"Task":"Review","Status":"Neutral","Priority":"Low"}]</table>
```

### Example 4: Suffix Column Combining

**Input**:
```html
<table>
<tr><th>Name</th><th>Name_2</th><th>Details</th><th>Details_2</th></tr>
<tr><td>John</td><td>Smith</td><td>Manager</td><td>Senior</td></tr>
</table>
```

**Processing Flow**:
1. `correct_tables()` → finds table
2. `process_tables()` → extracts structure
3. `classify_table_from_data()` → "simple"
4. `convert_to_key_value_json()` → standard conversion
5. `has_suffix_columns()` → detects _2 suffixes
6. `combine_suffix_columns()` → combines with comma separation

**Output**:
```html
<table>[{"Name":"John,Smith","Details":"Manager,Senior"}]</table>
```

## Performance and Error Handling

### Enhanced Error Handling
- Individual table processing prevents cascade failures
- Dual parser fallback (lxml → BeautifulSoup) for malformed HTML
- Enhanced encoding detection and handling
- Graceful degradation for edge cases

### Memory Efficiency
- In-memory processing without temporary files
- Streaming table processing for large documents
- Efficient matrix operations for span handling
- Optimized JSON serialization

### UTF-16/UTF-8 BOM Support
- Automatic encoding detection
- BOM character removal and normalization
- Enhanced Unicode symbol processing
- Fallback mechanisms for encoding issues

This enhanced documentation reflects all improvements and fixes in finalcode2.py, with particular emphasis on the critical column disappearing fix and UTF-16/UTF-8 BOM enhancements.
