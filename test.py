#!/usr/bin/env python3
"""
Basic Table Processing Test Script

This script tests the table processing functionality on a limited set of files:
- di_parser_output.txt (original dataset)
- trial1.txt (additional test cases)
- trial2.txt (additional test cases)
- trial24.txt (symbol replacement test cases)
- trial25.txt (colspan header test cases)

SCOPE: Basic testing with 5 files (89 tables total)
FUNCTION TESTED: correct_tables() from code_version_2.py
OUTPUT: Console output showing processing results

This provides basic testing coverage for the core table conversion functionality
without the comprehensive scope of the full test suite. Used for quick validation
of core functionality during development.

Key Features Tested:
- HTML table extraction and parsing (process_tables)
- Table structure classification (classify_table_from_data)
- JSON conversion for various table types (convert_to_key_value_json)
- Column suffix combining (_2, _3, _4 patterns with comma separation)
- Caption preservation and formatting
- Error handling and fallback strategies (lxml → BeautifulSoup)
- Individual table processing workflow
"""

import os
import code_version_2

def test_basic_files():
    """
    Test di_parser_output.txt and trial1-2 files only.

    This function processes a limited set of test files to validate
    core table processing functionality without the full test suite scope.
    """

    print("=== BASIC TEST: DI_PARSER + TRIAL1-2 + SYMBOLS + COLSPAN ===")
    print()

    # Test files to process
    test_files = []

    # Add di_parser_output.txt if it exists
    if os.path.exists('di_parser_output.txt'):
        test_files.append('di_parser_output.txt')

    # Add trial1.txt, trial2.txt, and trial24.txt if they exist
    for trial_num in [1, 2]:
        trial_file = f'test/trial{trial_num}.txt'
        if os.path.exists(trial_file):
            test_files.append(trial_file)

    # Add trial24.txt (symbol test cases)
    trial24_file = 'test/trial24.txt'
    if os.path.exists(trial24_file):
        test_files.append(trial24_file)

    # Add trial25.txt (colspan header test cases)
    trial25_file = 'test/trial25.txt'
    if os.path.exists(trial25_file):
        test_files.append(trial25_file)

    if not test_files:
        print("❌ No test files found!")
        return []

    print(f"Found {len(test_files)} test files")
    results = []

    for file_path in test_files:
        file_name = os.path.basename(file_path)
        print(f"\n--- Testing {file_name} ---")
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            result = code_version_2.correct_tables(content)
            table_count = result.count('<table>')
            input_length = len(content)
            output_length = len(result)

            print(f"✅ {file_name}: SUCCESS")
            print(f"   Tables processed: {table_count}")
            print(f"   Input length: {input_length:,} characters")
            print(f"   Output length: {output_length:,} characters")

            results.append((file_name, table_count, input_length, output_length))

            # Show sample output for first table
            if table_count > 0:
                first_table_start = result.find('<table>')
                first_table_end = result.find('</table>', first_table_start) + 8
                first_table = result[first_table_start:first_table_end]
                sample = first_table[:200] + "..." if len(first_table) > 200 else first_table
                print(f"   Sample output: {sample}")

        except Exception as e:
            print(f"❌ {file_name}: ERROR - {str(e)}")
            results.append((file_name, 0, 0, 0))

    print("\n" + "="*60)
    print("=== BASIC TESTING SUMMARY ===")
    total_tables = sum(r[1] for r in results)
    total_input = sum(r[2] for r in results)
    total_output = sum(r[3] for r in results)

    print(f"Total files tested: {len(test_files)}")
    print(f"Total tables processed: {total_tables}")
    print(f"Total input characters: {total_input:,}")
    print(f"Total output characters: {total_output:,}")

    if total_input > 0:
        print(f"Processing efficiency: {((total_input - total_output) / total_input * 100):.1f}% size reduction")

    print(f"\nDetailed Results:")
    for file, tables, inp, out in results:
        if tables > 0:
            efficiency = ((inp - out) / inp * 100) if inp > 0 else 0
            print(f"  {file}: {tables} tables ({inp:,} → {out:,} chars, {efficiency:.1f}% reduction)")
        else:
            print(f"  {file}: FAILED")

    success_rate = (len([r for r in results if r[1] > 0]) / len(results)) * 100
    print(f"\nOverall success rate: {success_rate:.0f}%")

    if success_rate == 100:
        print("🎯 BASIC TESTS PASSED - DI_PARSER + TRIAL1-2 WORKING!")
    else:
        print("⚠️  Some basic tests failed - need investigation")

    return results

if __name__ == "__main__":
    test_basic_files()
