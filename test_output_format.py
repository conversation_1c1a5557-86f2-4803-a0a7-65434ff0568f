#!/usr/bin/env python3
"""
Test to verify output format matches original exactly.
"""

import code_version_1 as original_code
import code_version_2 as adaptive_code

def test_output_format():
    """Test that output format is identical to original."""
    print("🚀 Testing Output Format Compatibility")
    print("=" * 50)
    
    # Test with simple example
    test_html = """{'content': '<table><tr><th>Name</th><th>Age</th></tr><tr><td>John</td><td>25</td></tr><tr><td>Mary</td><td>30</td></tr></table>', 'language': 'en'}"""
    
    print("📝 Processing test HTML...")
    
    # Process with original
    print("\n🔍 Original code output:")
    original_result = original_code.correct_tables(test_html)
    
    print("\n🔍 Adaptive code output:")
    adaptive_result = adaptive_code.correct_tables(test_html)
    
    # Compare results
    print(f"\n📊 COMPARISON")
    print("=" * 50)
    
    if original_result == adaptive_result:
        print("✅ Output formats are IDENTICAL")
    else:
        print("❌ Output formats differ")
        print(f"Original length: {len(original_result) if original_result else 0}")
        print(f"Adaptive length: {len(adaptive_result) if adaptive_result else 0}")
    
    # Test output structure
    print(f"\n📋 OUTPUT STRUCTURE")
    print("=" * 50)
    
    if adaptive_result:
        print(f"✅ Adaptive code returns string result")
        print(f"✅ Length: {len(adaptive_result)} characters")
        
        # Check if it contains expected elements
        if "{'content':" in adaptive_result:
            print("✅ Contains content wrapper")
        if "<table>" in adaptive_result:
            print("✅ Contains table tags")
        if "'language': 'en'}" in adaptive_result:
            print("✅ Contains language specification")
    else:
        print("❌ No result returned")
    
    return True

if __name__ == "__main__":
    test_output_format()
