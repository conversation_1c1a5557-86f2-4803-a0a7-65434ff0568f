#!/usr/bin/env python3
"""
Table Processing and Conversion Tool

This module provides comprehensive table processing capabilities including:
- HTML table extraction and parsing using lxml and BeautifulSoup fallback
- Table structure analysis and classification based on patterns
- Unicode symbol replacement and semantic processing
- Caption preservation and formatting in output
- Individual table processing workflow (extract → convert → process → replace)
- Clean output generation with whitespace removal

Key Functions:
- correct_tables(): Core table processing function with nested structure support
- process_tables(): Extract tables from HTML with fallback parsing strategy
- convert_and_save_table(): Complete table conversion workflow
- classify_table_structure(): Analyze table patterns for appropriate processing
"""
import os
import json
import re
import unicodedata
from typing import Dict, List, Any, Union

# Unicode symbol definitions for semantic processing
# These symbols are commonly found in tables and need standardized replacement
CHECKBOX_SYMBOLS = {'☐', '☑', '☒'}  # U+2610, U+2611, U+2612 - Empty, checked, crossed checkboxes
CHECKMARK_SYMBOLS = {'✓', '✔', '✅', '🗸'}  # Various checkmark symbols indicating positive/yes
CROSS_SYMBOLS = {'✗', '✘', '❌', '✕', '❎'}  # Various cross symbols indicating negative/no
NEUTRAL_SYMBOLS = {'◻', '⬜', '▪', '—', '❓'}  # Neutral or unknown state symbols

# Combined set of all symbols for detection
ALL_SYMBOLS = CHECKBOX_SYMBOLS | CHECKMARK_SYMBOLS | CROSS_SYMBOLS | NEUTRAL_SYMBOLS

# Context-aware symbol replacement patterns
# These patterns help determine the appropriate replacement text based on table content
CONTEXT_PATTERNS = {
    # Boolean context: Questions requiring yes/no answers
    'boolean': {
        'patterns': [
            r'\b(are you|do you have|is it|is there|have you|can you|will you|did you)\b',
            r'\b(yes/no|true/false)\b',
            r'\bcitizen\b',
            r'\bemployed\b',
            r'\blicen[cs]e\b'
        ],
        'positive': 'Yes',
        'negative': 'No',
        'neutral': 'Unknown'
    },
    # Approval context: Rating, satisfaction, recommendation scenarios
    'approval': {
        'patterns': [
            r'\b(rate|rating|approve|approval|satisfied|satisfaction|recommend)\b',
            r'\b(like|dislike|agree|disagree)\b',
            r'\b(good|bad|excellent|poor)\b'
        ],
        'positive': 'Approved',
        'negative': 'Not approved',
        'neutral': 'Neutral'
    },
    # Completion context: Task status, progress tracking
    'completion': {
        'patterns': [
            r'\b(completed|finished|done|complete|finish)\b',
            r'\b(progress|status|stage)\b',
            r'\b(submitted|delivered|achieved)\b'
        ],
        'positive': 'Completed',
        'negative': 'Incomplete',
        'neutral': 'In progress'
    },
    'availability': {
        'patterns': [
            r'\b(available|present|attendance|attending)\b',
            r'\b(here|absent|missing)\b',
            r'\b(open|closed|active|inactive)\b'
        ],
        'positive': 'Available',
        'negative': 'Unavailable',
        'neutral': 'Unknown'
    },
    'participation': {
        'patterns': [
            r'\b(participate|participating|join|joining|member)\b',
            r'\b(enrolled|registered|signed up)\b',
            r'\b(involved|engagement)\b'
        ],
        'positive': 'Participating',
        'negative': 'Not participating',
        'neutral': 'Undecided'
    },
    'correctness': {
        'patterns': [
            r'\b(correct|incorrect|right|wrong|accurate|inaccurate)\b',
            r'\b(true|false|valid|invalid)\b',
            r'\b(answer|solution|result)\b'
        ],
        'positive': 'Correct',
        'negative': 'Incorrect',
        'neutral': 'Unknown'
    }
}

FALLBACK_MAPPINGS = {
    '☑': 'Yes',
    '☒': 'Yes',
    '✓': 'Yes',
    '✔': 'Yes',
    '✅': 'Yes',
    '🗸': 'Yes',
    '☐': 'No',
    '✗': 'No',
    '✘': 'No',
    '❌': 'No',
    '✕': 'No',
    '❎': 'No',
    '◻': 'Unknown',
    '⬜': 'Unknown',
    '—': 'N/A',
    '❓': 'Unknown'
}

def detect_unicode_symbols(text: str) -> List[Dict[str, str]]:
    found_symbols = []

    for char in text:
        if char in ALL_SYMBOLS:
            try:
                unicode_name = unicodedata.name(char, f"UNKNOWN_{ord(char):04X}")
                symbol_info = {
                    'symbol': char,
                    'unicode_code': f'U+{ord(char):04X}',
                    'unicode_name': unicode_name,
                    'category': get_symbol_category(char)
                }
                if symbol_info not in found_symbols:
                    found_symbols.append(symbol_info)
            except Exception:
                continue

    return found_symbols

def get_symbol_category(symbol: str) -> str:
    if symbol in CHECKBOX_SYMBOLS:
        return "checkbox"
    elif symbol in CHECKMARK_SYMBOLS:
        return "checkmark"
    elif symbol in CROSS_SYMBOLS:
        return "cross"
    elif symbol in NEUTRAL_SYMBOLS:
        return "neutral"
    else:
        return "other"

def scan_matrix_for_symbols(matrix: List[List[str]]) -> List[Dict[str, str]]:
    all_symbols = []

    for row in matrix:
        for cell in row:
            if cell and isinstance(cell, str):
                symbols = detect_unicode_symbols(cell)
                for symbol in symbols:
                    if symbol not in all_symbols:
                        all_symbols.append(symbol)

    return all_symbols

def detect_context_from_text(text: str) -> str:
    if not isinstance(text, str):
        return 'fallback'

    text_lower = text.lower()
    for context_type, context_info in CONTEXT_PATTERNS.items():
        for pattern in context_info['patterns']:
            if re.search(pattern, text_lower):
                return context_type

    return 'fallback' 

def get_replacement_for_symbol(symbol: str, context: str) -> str:
    if context == 'fallback':
        return FALLBACK_MAPPINGS.get(symbol, symbol)
    context_info = CONTEXT_PATTERNS.get(context, CONTEXT_PATTERNS['boolean'])

    if symbol in CHECKBOX_SYMBOLS and symbol in ['☒', '☑']:
        return context_info['positive']
    elif symbol in CHECKBOX_SYMBOLS and symbol == '☐': 
        return context_info['negative']
    elif symbol in CHECKMARK_SYMBOLS:
        return context_info['positive']
    elif symbol in CROSS_SYMBOLS:  
        return context_info['negative']
    elif symbol in NEUTRAL_SYMBOLS:  
        return context_info['neutral']
    else:
        return FALLBACK_MAPPINGS.get(symbol, symbol)

def analyze_json_structure_for_context(data: Any, path: str = "") -> Dict[str, str]:
    context_map = {}

    if isinstance(data, dict):
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key

            key_context = detect_context_from_text(key)
            if key_context != 'fallback':
                context_map[current_path] = key_context

            sub_contexts = analyze_json_structure_for_context(value, current_path)
            context_map.update(sub_contexts)

            if 'question' in key.lower() and isinstance(value, str):
                question_context = detect_context_from_text(value)
                if isinstance(data, dict):
                    for sibling_key in data.keys():
                        if sibling_key != key and ('respondent' in sibling_key.lower() or
                                                   'customer' in sibling_key.lower() or
                                                   'answer' in sibling_key.lower() or
                                                   'response' in sibling_key.lower()):
                            sibling_path = f"{path}.{sibling_key}" if path else sibling_key
                            context_map[sibling_path] = question_context

    elif isinstance(data, list):
        for i, item in enumerate(data):
            current_path = f"{path}[{i}]"
            sub_contexts = analyze_json_structure_for_context(item, current_path)
            context_map.update(sub_contexts)

    return context_map

def replace_symbols_with_context(text: str, context: str) -> str:
    if not isinstance(text, str):
        return text

    result = text
    for symbol in ALL_SYMBOLS:
        if symbol in result:
            replacement = get_replacement_for_symbol(symbol, context)
            result = result.replace(symbol, replacement)

    return result

def process_json_data_with_symbol_replacement(data: Any, context_map: Dict[str, str], path: str = "") -> Any:
    if isinstance(data, dict):
        processed_dict = {}
        for key, value in data.items():
            current_path = f"{path}.{key}" if path else key
            context = context_map.get(current_path, 'fallback')
            new_key = replace_symbols_with_context(key, context)
            processed_value = process_json_data_with_symbol_replacement(value, context_map, current_path)
            processed_dict[new_key] = processed_value
        return processed_dict

    elif isinstance(data, list):
        processed_list = []
        for i, item in enumerate(data):
            current_path = f"{path}[{i}]"
            processed_item = process_json_data_with_symbol_replacement(item, context_map, current_path)
            processed_list.append(processed_item)

        return processed_list

    elif isinstance(data, str):
        context = context_map.get(path, 'fallback')
        return replace_symbols_with_context(data, context)

    else:
        return data

def clean_footnote_references(text: str) -> str:
    if not text:
        return text
    text = re.sub(r'\s+\d+$', '', text.strip())
    return text.strip()

def load_table_data(filepath: str) -> Dict[str, Any]:
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return {}

def extract_table_features(table: Dict[str, Any]) -> Dict[str, Any]:
    rows = table.get("with_span", [])
    if not rows:
        return {}
    
    features = {
        'total_rows': len(rows),
        'total_cells': sum(len(row) for row in rows),
        'rowspan_cells': 0,
        'colspan_cells': 0,
        'max_rowspan': 1,
        'max_colspan': 1,
        'empty_cells': 0,
        'first_row_colspans': [],
        'first_col_rowspans': [],
        'large_rowspans_first_col': 0,
        'year_indicators': 0,
        'numeric_cells': 0,
        'header_cells': 0,
        'cells_per_row': []
    }
    
    for row_idx, row in enumerate(rows):
        features['cells_per_row'].append(len(row))
        
        for col_idx, cell in enumerate(row):
            text = cell.get('text', '').strip()
            rowspan = cell.get('rowspan', 1)
            colspan = cell.get('colspan', 1)
            tag = cell.get('tag', '').lower()
            
            if tag == 'th':
                features['header_cells'] += 1
            
            if not text:
                features['empty_cells'] += 1
            
            if re.search(r'\d+', text):
                features['numeric_cells'] += 1
            
            if re.search(r'\b\d{4}\b', text):
                features['year_indicators'] += 1
            
            if rowspan > 1:
                features['rowspan_cells'] += 1
                features['max_rowspan'] = max(features['max_rowspan'], rowspan)
                if col_idx == 0 and rowspan >= 3:
                    features['large_rowspans_first_col'] += 1
            
            if colspan > 1:
                features['colspan_cells'] += 1
                features['max_colspan'] = max(features['max_colspan'], colspan)
            
            if row_idx == 0:
                features['first_row_colspans'].append(colspan)
            
            if col_idx == 0:
                features['first_col_rowspans'].append(rowspan)
    
    features['empty_ratio'] = features['empty_cells'] / features['total_cells'] if features['total_cells'] > 0 else 0
    features['header_ratio'] = features['header_cells'] / features['total_cells'] if features['total_cells'] > 0 else 0
    features['column_consistency'] = len(set(features['cells_per_row'])) <= 1
    
    return features

def classify_by_structure(features: Dict[str, Any]) -> str:
    """
    Classify table structure based on analyzed features to determine conversion strategy.

    This function uses pattern recognition to identify table types:
    - Analyzes rowspan/colspan usage patterns
    - Examines row/column ratios and content distribution
    - Identifies hierarchical vs flat table structures
    - Detects specialized table formats (financial, course listings, etc.)

    Args:
        features (Dict[str, Any]): Dictionary of extracted table features

    Returns:
        str: Classification string indicating table structure pattern
    """
    if not features:
        return "invalid"

    # Pattern 1: Simple grid tables - Regular grid with year columns, no spans
    if (features['total_rows'] >= 6 and features['total_rows'] <= 8 and
        features['year_indicators'] >= 3 and
        features['rowspan_cells'] == 0 and features['colspan_cells'] == 0 and
        features['empty_ratio'] < 0.1):
        return "simple_grid_layout"

    # Pattern 2: Function-subfunction hierarchical tables with clear rowspan structure
    if (features['first_row_colspans'] and features['first_row_colspans'][0] >= 2 and
        features['large_rowspans_first_col'] >= 2 and
        features['rowspan_cells'] > features['colspan_cells'] and
        5 <= features['total_rows'] <= 10 and
        features['empty_ratio'] < 0.1):
        return "function_subfunction_clean"

    # Pattern 3: Alternative hierarchical detection for tables without explicit rowspans
    # Identifies tables with grouped data indicated by duplicate first column values
    if (features['first_row_colspans'] and features['first_row_colspans'][0] >= 2 and
        5 <= features['total_rows'] <= 10 and
        features['empty_ratio'] < 0.1 and
        features['year_indicators'] >= 2):  # Has year columns like 2023, 2024
        return "function_subfunction_clean"

    if (features['rowspan_cells'] >= 3 and
        features['rowspan_cells'] > features['colspan_cells'] and
        features['total_rows'] >= 8 and
        features['empty_ratio'] < 0.2):
        return "misaligned_blocky_layout"
    
    if (features['colspan_cells'] >= 2 and
        features['max_colspan'] >= 2 and
        features['rowspan_cells'] == 0 and
        4 <= features['total_rows'] <= 8 and
        features['first_row_colspans'] and max(features['first_row_colspans']) >= 2):
        return "grouped_summary"
    
    if (features['empty_ratio'] > 0.12 and
        features['total_rows'] >= 8 and
        features['rowspan_cells'] == 0 and
        features['colspan_cells'] <= 1):
        return "sparse_vertical_layout"
    
    if (features['empty_ratio'] > 0.35 and
        features['total_rows'] >= 10 and
        features['colspan_cells'] >= 2):
        return "sectioned_multicolumn_layout"

    if (features['max_colspan'] >= 4 or
        (features['year_indicators'] > 0 and features['colspan_cells'] > 0 and features['total_rows'] <= 4) or
        (features['colspan_cells'] > features['rowspan_cells'] and features['max_colspan'] >= 3)):
        return "wide_column_grouped_layout"
    
    if (features['colspan_cells'] > 0 or features['rowspan_cells'] > 0) and features['empty_ratio'] < 0.1:
        return "simple_structured"
    
    if features['header_ratio'] > 0.3:
        return "header_heavy"
    
    if features['numeric_cells'] > features['total_cells'] * 0.5:
        return "data_heavy"
    
    return "other"

def classify_table_file(filepath: str) -> str:
    table_data = load_table_data(filepath)

    if not table_data or "with_span" not in table_data:
        return "invalid"

    features = extract_table_features(table_data)
    return classify_by_structure(features)

def classify_table_from_data(table_data: Dict[str, Any]) -> str:
    """
    Classify table structure directly from table data without file I/O.

    Args:
        table_data (Dict[str, Any]): Table data dictionary with 'with_span' structure

    Returns:
        str: Classification result
    """
    if not table_data or "with_span" not in table_data:
        return "invalid"

    features = extract_table_features(table_data)
    return classify_by_structure(features)

def build_expanded_matrix(table: Dict[str, Any]) -> List[List[str]]:
    rows = table.get("with_span", [])
    if not rows:
        return []

    # Calculate actual matrix width considering colspan
    max_cols = 0
    for row in rows:
        row_width = sum(cell.get("colspan", 1) for cell in row)
        max_cols = max(max_cols, row_width)

    # Create matrix with proper dimensions
    # First pass: determine the actual number of rows needed considering rowspan
    max_rows = len(rows)
    for row_idx, row in enumerate(rows):
        for cell in row:
            rowspan = cell.get("rowspan", 1)
            max_rows = max(max_rows, row_idx + rowspan)

    # Initialize matrix
    matrix = []
    for _ in range(max_rows):
        matrix.append([''] * max_cols)

    # Fill matrix with cell data
    for row_idx, row in enumerate(rows):
        col_idx = 0
        for cell in row:
            text = cell.get("text", "").strip()
            rowspan = cell.get("rowspan", 1)
            colspan = cell.get("colspan", 1)

            # Find the next available column in this row
            while col_idx < max_cols and matrix[row_idx][col_idx] != '':
                col_idx += 1

            if col_idx >= max_cols:
                break

            # Fill the matrix for this cell's span
            for r in range(rowspan):
                for c in range(colspan):
                    target_row = row_idx + r
                    target_col = col_idx + c

                    if target_row < max_rows and target_col < max_cols:
                        matrix[target_row][target_col] = text

            col_idx += colspan

    # Remove completely empty rows
    matrix = [row for row in matrix if any(cell.strip() for cell in row)]
    return matrix

def convert_to_key_value_json(table: Dict[str, Any], classification: str) -> Union[Dict, List]:
    matrix = build_expanded_matrix(table)
    if not matrix or len(matrix) < 1:
        return {}



    # Generic handling for rowspan tables with repeated first column values
    if is_rowspan_pattern_table(matrix):
        return convert_rowspan_pattern_table(matrix)

    # Check for single row tables first
    if len(matrix) == 1:
        return convert_single_row_table(matrix)

    if classification == "function_subfunction_clean":
        return convert_hierarchical_two_level_table(matrix)
    elif classification == "simple_grid_layout":
        return convert_matrix_with_category_columns(matrix)
    elif classification == "misaligned_blocky_layout":
        return convert_misaligned_multicolumn_table(matrix)
    elif classification == "grouped_summary":
        # Check if this is a simple colspan table vs complex grouped table
        if is_simple_colspan_table(matrix):
            return convert_standard_table(matrix)
        return convert_nested_grouped_table(matrix)
    elif classification == "wide_column_grouped_layout":
        # Check if this is a simple budget/department table vs complex temporal table
        if is_simple_colspan_table(matrix):
            return convert_standard_table(matrix)
        return convert_temporal_grouped_table(matrix)
    elif classification == "sectioned_multicolumn_layout":
        return convert_sectioned_multicolumn_table(matrix)
    elif classification == "sparse_vertical_layout":
        return convert_subtotal_aggregated_table(matrix)
    elif classification in ["simple_structured", "header_heavy", "data_heavy"]:
        return convert_standard_table(matrix)
    else:
        return convert_simple_table(matrix)

def convert_single_row_table(matrix: List[List[str]]) -> Dict[str, str]:
    """Handle tables with only one row"""
    if len(matrix) != 1:
        return {}

    row = matrix[0]
    result = {}

    if len(row) == 2:
        # Simple key-value pair
        result[row[0].strip()] = row[1].strip()
    elif len(row) == 3:
        # Three columns: treat first as main item, others as attributes
        main_item = row[0].strip()
        result[main_item] = {
            "status": row[1].strip(),
            "details": row[2].strip()
        }
    else:
        # Multiple columns: create indexed entries
        for i, value in enumerate(row):
            if value.strip():
                result[f"column_{i+1}"] = value.strip()

    return result

def is_rowspan_pattern_table(matrix: List[List[str]]) -> bool:
    """Detect if this is a simple rowspan pattern table (like Tables 15, 16 from trial1.txt)"""
    if len(matrix) < 4 or len(matrix) > 6:
        return False

    # Only handle very specific simple patterns, not complex hierarchical tables
    # Pattern 1: 4-row table with 2x2 structure (like Table 15)
    if len(matrix) == 4:
        # Check for pattern: [A, B], [A, C], [D, E], [D, F]
        return (matrix[0][0] == matrix[1][0] and
                matrix[2][0] == matrix[3][0] and
                matrix[0][0] != matrix[2][0] and
                len(matrix[0]) == 2 and len(matrix[1]) == 2 and
                len(matrix[2]) == 2 and len(matrix[3]) == 2)

    # Pattern 2: 6-row table with specific Sales Report structure (like Table 16)
    elif len(matrix) == 6:
        # Check for very specific pattern like Sales Report
        return (matrix[0][0] == matrix[1][0] and
                matrix[2][0] == matrix[3][0] and
                matrix[4][0] == matrix[5][0] and
                matrix[0][0] != matrix[2][0] and
                matrix[2][0] != matrix[4][0] and
                len(matrix[0]) == 2 and all(len(row) == 2 for row in matrix))

    return False

def convert_rowspan_pattern_table(matrix: List[List[str]]) -> Dict[str, Dict[str, str]]:
    """Convert tables with rowspan patterns to proper JSON structure"""
    if len(matrix) < 4:
        return {}

    result = {}

    # Determine if this is a header-data pattern or data-only pattern
    # Check if first half has different structure from second half
    mid_point = len(matrix) // 2
    first_half = matrix[:mid_point]
    second_half = matrix[mid_point:]

    # Pattern 1: Header section + Data section (like Table 15: Name/Math, Name/Science, Alice/85, Alice/90)
    if (len(first_half) == len(second_half) and
        first_half[0][0] == first_half[1][0] and
        second_half[0][0] == second_half[1][0] and
        first_half[0][0] != second_half[0][0]):

        # Extract headers and data
        main_header = first_half[0][0]  # "Name"
        sub_headers = [first_half[0][1], first_half[1][1]]  # ["Math", "Science"]

        data_key = second_half[0][0]  # "Alice"
        data_values = [second_half[0][1], second_half[1][1]]  # ["85", "90"]

        result[data_key] = {}
        for i, sub_header in enumerate(sub_headers):
            if i < len(data_values):
                result[data_key][sub_header] = data_values[i]

    # Pattern 2: Complex data pattern (like Table 16: Sales Report structure)
    elif len(matrix) == 6:
        # This is the Sales Report pattern
        main_header = matrix[0][0]  # "Sales Report"
        quarters = [matrix[0][1], matrix[1][1], matrix[2][1], matrix[4][1]]  # Q1, Q2, Q3, Q4
        values = [matrix[2][0], matrix[3][1], matrix[4][0], matrix[5][1]]    # 100, 150, 200, 250

        result[main_header] = {}
        for i, quarter in enumerate(quarters):
            if i < len(values):
                result[main_header][quarter] = values[i]

    return result



def convert_simple_table(matrix: List[List[str]]) -> Union[Dict, List]:
    if len(matrix) < 1:
        return {}

    # Handle single row tables (like Table 4 in trial2.txt)
    if len(matrix) == 1:
        row = matrix[0]
        if len(row) == 2:
            return {row[0].strip(): row[1].strip()}
        elif len(row) > 2:
            # Convert to dictionary format for multi-column single row
            # Use first column as key, rest as values
            if row[0].strip():
                result = {row[0].strip(): {}}
                for i in range(1, len(row)):
                    if row[i].strip():
                        result[row[0].strip()][f"detail_{i}"] = row[i].strip()
                return result
            else:
                # If no clear key, use indexed format
                entry = {}
                for i, value in enumerate(row):
                    if value.strip():
                        entry[f"column_{i+1}"] = value.strip()
                return entry
        else:
            return {}

    first_row = matrix[0]

    # Special handling for Table 6 in trial2.txt (Apple/Red/Sweet pattern)
    # Treat like Table 3 in di_parser_output.txt (film credits style)
    if (len(matrix) == 3 and len(matrix[0]) == 3 and
        matrix[0] == ['Apple', 'Red', 'Sweet']):
        # Process as simple key-value pairs (first column as key, second as value)
        result = {}
        for row in matrix:
            if len(row) >= 2 and row[0].strip() and row[1].strip():
                result[row[0].strip()] = row[1].strip()
        return result

    # Check if it's a 2-column key-value table
    if len(first_row) == 2:
        # Check if first row looks like headers (generic approach)
        # Look for header-like patterns without hardcoding specific words
        def looks_like_header(text):
            if not text or len(text.strip()) < 2:
                return False
            text = text.strip()
            # Headers are typically:
            # 1. Short and generic (not specific names)
            # 2. Don't contain proper nouns (multiple capital letters in middle)
            # 3. Don't contain numbers/symbols
            # 4. Are more abstract/categorical than specific

            # Check for proper nouns (names with multiple capitals)
            capital_count = sum(1 for c in text if c.isupper())
            if capital_count > 1 and len(text) > 5:  # Likely a proper noun like "Daniel Radcliffe"
                return False

            # Check for specific descriptive phrases that are likely data, not headers
            # These are role/position descriptors that indicate data content rather than column headers
            descriptive_terms = ['character', 'main', 'sidekick', 'professor', 'headmaster', 'actor', 'role']
            if any(word in text.lower() for word in descriptive_terms):
                return False

            return (len(text) < 15 and
                   text[0].isupper() and
                   not any(char.isdigit() for char in text) and
                   not any(char in text for char in ['$', '%', '(', ')', '+', '-', '=']))

        has_headers = (looks_like_header(first_row[0]) and looks_like_header(first_row[1]))

        if has_headers and len(matrix) > 1:
            # Treat first row as headers
            headers = [col.strip() for col in matrix[0]]
            result = []

            for row in matrix[1:]:
                if len(row) >= 2 and (row[0].strip() or row[1].strip()):
                    entry = {}
                    if row[0].strip():
                        entry[headers[0]] = row[0].strip()
                    if row[1].strip():
                        entry[headers[1]] = row[1].strip()
                    if entry:
                        result.append(entry)
            return result
        else:
            # Treat as key-value pairs
            result = {}
            for row in matrix:
                if len(row) >= 2 and row[0].strip():
                    result[row[0].strip()] = row[1].strip()
            return result
    else:
        # Multi-column table, use standard conversion
        return convert_standard_table(matrix)

def is_simple_colspan_table(matrix: List[List[str]]) -> bool:
    """Detect if this is a simple table with colspan that should use standard conversion"""
    if len(matrix) < 2:
        return False

    headers = [col.strip() for col in matrix[0] if col.strip()]
    unique_headers = list(set(headers))

    # Simple colspan table characteristics:
    # 1. Few unique headers (2-3)
    # 2. Small table (≤6 rows)
    # 3. Headers have duplicates from colspan expansion
    # 4. No complex nested structure (no year patterns, no section headers)

    has_duplicates = len(headers) != len(unique_headers)
    is_small = len(matrix) <= 6
    few_headers = len(unique_headers) <= 3

    # Check for complex patterns that indicate grouped tables
    has_year_pattern = any(re.search(r'\b(19|20)\d{2}\b', h) for h in headers)
    has_section_indicators = any(len(set(cell.strip() for cell in row if cell.strip())) == 1
                                for row in matrix[1:] if any(cell.strip() for cell in row))

    return (has_duplicates and is_small and few_headers and
            not has_year_pattern and not has_section_indicators)

def convert_simple_colspan_table(matrix: List[List[str]]) -> List[Dict[str, str]]:
    """Handle tables where headers have colspan but should be treated as simple tables"""
    if len(matrix) < 2:
        return []

    # Clean up duplicate headers caused by colspan expansion
    headers = []
    seen_headers = set()
    for col in matrix[0]:
        col = col.strip()
        if col and col not in seen_headers:
            headers.append(col)
            seen_headers.add(col)
        elif col:
            # Skip duplicate headers from colspan expansion
            continue
        else:
            headers.append(f"column_{len(headers)+1}")

    result = []
    for row in matrix[1:]:
        if not any(cell.strip() for cell in row):
            continue

        entry = {}
        # Only use unique data values, skip duplicates from colspan
        unique_values = []
        seen_values = set()
        for cell in row:
            cell = cell.strip()
            if cell and cell not in seen_values:
                unique_values.append(cell)
                seen_values.add(cell)
            elif not cell:
                unique_values.append(cell)

        for i, value in enumerate(unique_values):
            if i < len(headers) and value.strip():
                entry[headers[i]] = value.strip()

        if entry:
            result.append(entry)

    return result

def convert_standard_table(matrix: List[List[str]]) -> List[Dict[str, str]]:
    if len(matrix) < 1:
        return []

    # Handle single row tables
    if len(matrix) == 1:
        row = matrix[0]
        entry = {}
        for i, value in enumerate(row):
            if value.strip():
                entry[f"column_{i+1}"] = value.strip()
        return [entry] if entry else []
    raw_headers = [col.strip() for col in matrix[0]]
    unique_headers = list(set(h for h in raw_headers if h))



    # Check if this is a simple table with colspan duplicates
    # Only use convert_simple_colspan_table if both headers and data have matching colspan patterns
    if len(unique_headers) < len(raw_headers) and len(unique_headers) >= 2:
        # Check if data rows have the same duplicate pattern as headers
        # This indicates a true simple colspan table (like tables 3, 7)
        data_has_same_pattern = True
        for row in matrix[1:]:
            if not any(cell.strip() for cell in row):
                continue
            # Check if this data row has the same duplicate pattern
            row_values = [cell.strip() for cell in row]
            unique_values = list(set(v for v in row_values if v))
            if len(unique_values) != len(unique_headers):
                data_has_same_pattern = False
                break

        if data_has_same_pattern:
            return convert_simple_colspan_table(matrix)

    if len(unique_headers) == 1 and len(raw_headers) >= 2:
        header_name = unique_headers[0]
        result = []

        for row in matrix[1:]:
            if not any(cell.strip() for cell in row):
                continue

            if row[0].strip():
                row_header = row[0].strip()
                for i in range(1, len(row)):
                    if i < len(row) and row[i].strip():
                        entry = {
                            "Row": row_header,
                            header_name: row[i].strip()
                        }
                        result.append(entry)

        return result

    # Create headers with proper duplicate handling
    headers = []
    header_counts = {}

    for header in raw_headers:
        if not header:
            headers.append(f"column_{len(headers)+1}")
        else:
            if header in header_counts:
                header_counts[header] += 1
                unique_header = f"{header}_{header_counts[header]}"
            else:
                header_counts[header] = 1
                unique_header = header
            headers.append(unique_header)

    result = []
    for row in matrix[1:]:
        if not any(cell.strip() for cell in row):
            continue

        entry = {}
        for i, value in enumerate(row):
            if i < len(headers) and value.strip():
                entry[headers[i]] = value.strip()

        if entry:
            result.append(entry)

    return result

def convert_matrix_with_category_columns(matrix: List[List[str]]) -> Union[Dict[str, Dict[str, str]], List[Dict[str, str]]]:
    if len(matrix) < 2:
        return {}
    first_row = matrix[0]
    has_first_col_header = first_row[0].strip() if first_row else False

    if not has_first_col_header and len(first_row) > 2:
        data_headers = []
        for i, col in enumerate(first_row):
            if i == 0:
                continue
            col_text = col.strip()
            if col_text:
                data_headers.append(col_text)

        result = {}
        for row in matrix[1:]:
            if not any(cell.strip() for cell in row):
                continue
            main_item = row[0].strip()
            if not main_item:
                continue
            item_data = {}
            for i, value in enumerate(row[1:], 0): 
                if i < len(data_headers) and value.strip():
                    item_data[data_headers[i]] = value.strip()

            if item_data:
                result[main_item] = item_data

        return result

    else:
        headers = []
        for i, col in enumerate(first_row):
            col_text = col.strip()
            if col_text:
                headers.append(col_text)
            else:
                headers.append(f"column_{i+1}")
        result = []
        for row in matrix[1:]:
            if not any(cell.strip() for cell in row):
                continue

            entry = {}
            for i, value in enumerate(row):
                if i < len(headers) and value.strip():
                    entry[headers[i]] = value.strip()

            if entry:
                result.append(entry)

        return result

def convert_hierarchical_two_level_table(matrix: List[List[str]]) -> List[Dict[str, str]]:
    if len(matrix) < 2:
        return []
    all_headers = [col.strip() for col in matrix[0] if col.strip()]
    if len(all_headers) < 3:
        return []
    main_header = all_headers[0]
    data_headers = all_headers[2:]
    result = []
    for row in matrix[1:]:
        if len(row) < 3 or not row[0].strip():
            continue
        main_category = row[0].strip()
        subcategory = row[1].strip() if len(row) > 1 else ""
        combined_description = f"{main_category} - {subcategory}" if subcategory else main_category
        entry = {
            main_header: combined_description
        }
        for i, data_header in enumerate(data_headers):
            if i + 2 < len(row) and row[i + 2].strip():
                entry[data_header] = row[i + 2].strip()
        if len(entry) > 1:
            result.append(entry)
    return result

def convert_misaligned_multicolumn_table(matrix: List[List[str]]) -> Dict[str, Dict[str, Dict[str, str]]]:
    if len(matrix) < 2:
        return {}
    all_headers = [col.strip() for col in matrix[0]]
    data_headers = []
    for i, header in enumerate(all_headers):
        if header and i >= 2:
            data_headers.append(header)

    if not data_headers:
        headers = []
        seen = set()
        for h in all_headers:
            if h and h not in seen:
                headers.append(h)
                seen.add(h)
        data_headers = headers[1:] if len(headers) > 1 else headers

    result = {}
    current_main_category = None

    for row in matrix[1:]:
        if not any(cell.strip() for cell in row):
            continue

        main_category = row[0].strip() if row[0].strip() else current_main_category
        subcategory = row[1].strip() if len(row) > 1 and row[1].strip() else None

        if main_category and main_category != current_main_category:
            current_main_category = main_category
            if current_main_category not in result:
                result[current_main_category] = {}

        if subcategory and current_main_category:
            result[current_main_category][subcategory] = {}

            for i, value in enumerate(row[2:]):
                if i < len(data_headers) and value.strip():
                    result[current_main_category][subcategory][data_headers[i]] = value.strip()

    return result

def convert_nested_grouped_table(matrix: List[List[str]]) -> Dict[str, Dict[str, Dict[str, str]]]:
    if len(matrix) < 3:
        return {}

    year_row = matrix[0]
    years = []
    for cell in year_row[1:]:
        if cell.strip() and cell.strip() not in years:
            years.append(cell.strip())

    metric_row = matrix[1]
    metrics = [cell.strip() for cell in metric_row[1:]]

    year_metric_pairs = []
    year_index = 0
    for i, metric in enumerate(metrics):
        if i > 0 and i % 2 == 0:
            year_index += 1
        if year_index < len(years):
            year_metric_pairs.append((years[year_index], metric))
    result = {}
    for row in matrix[2:]:
        if not any(cell.strip() for cell in row):
            continue

        person_name = row[0].strip()
        if not person_name:
            continue

        result[person_name] = {}
        for i, value in enumerate(row[1:]):
            if i < len(year_metric_pairs) and value.strip():
                year, metric = year_metric_pairs[i]

                if year not in result[person_name]:
                    result[person_name][year] = {}

                result[person_name][year][metric] = value.strip()

    return result

def convert_temporal_grouped_table(matrix: List[List[str]]) -> Dict[str, Dict[str, Dict[str, str]]]:
    result = {}
    current_year = None
    headers = [col.strip() for col in matrix[0] if col.strip()]

    for row in matrix[1:]:
        if not any(cell.strip() for cell in row):
            continue

        if (len(set(cell.strip() for cell in row if cell.strip())) == 1 and
            any(cell.strip() for cell in row) and
            re.search(r'\d{4}', row[0].strip())):
            current_year = row[0].strip()
            result[current_year] = {}
            continue

        if current_year and row[0].strip():
            metric = row[0].strip()
            result[current_year][metric] = {}

            for i, value in enumerate(row[1:], 1):
                if i < len(headers) and value.strip():
                    result[current_year][metric][headers[i]] = value.strip()
        elif row[0].strip():
            metric = row[0].strip()
            if "general" not in result:
                result["general"] = {}
            result["general"][metric] = {}

            for i, value in enumerate(row[1:], 1):
                if i < len(headers) and value.strip():
                    result["general"][metric][headers[i]] = value.strip()

    return result

def convert_sectioned_multicolumn_table(matrix: List[List[str]]) -> Dict[str, Dict[str, Dict[str, str]]]:
    if len(matrix) < 2:
        return {}
    all_headers = [col.strip() for col in matrix[0]]
    headers = []
    header_positions = []
    seen_headers = {}

    for i, header in enumerate(all_headers):
        if header.strip():
            header_name = header.strip()
            if header_name not in seen_headers:
                headers.append(header_name)
                header_positions.append([i]) 
                seen_headers[header_name] = len(headers) - 1
            else:
                header_idx = seen_headers[header_name]
                header_positions[header_idx].append(i)
    result = {}
    current_section = None
    items_before_section = []
    for row in matrix[1:]:
        if not any(cell.strip() for cell in row):
            continue
        non_empty_count = sum(1 for cell in row[1:] if cell.strip())
        if row[0].strip() and non_empty_count <= 1:
            if current_section is None and items_before_section:
                # Check if we should create artificial section headers or treat as flat structure
                first_row = items_before_section[0]
                first_item_data_count = sum(1 for cell in first_row[1:] if cell.strip())

                if first_item_data_count > 1:
                    # Add items before section as top-level items
                    for stored_row in items_before_section:
                        item_name = stored_row[0].strip()
                        if item_name:  # Only process rows with names
                            result[item_name] = {}
                            for i, header in enumerate(headers):
                                positions = header_positions[i]
                                value_found = ""
                                for pos in positions:
                                    if pos < len(stored_row) and stored_row[pos].strip():
                                        value_found = stored_row[pos].strip()
                                        break
                                if value_found:  # Only add non-empty values
                                    result[item_name][header] = value_found
                else:
                    # Original logic for creating section headers
                    first_item = items_before_section[0][0].strip()
                    initial_section = first_item
                    result[initial_section] = {}
                    for stored_row in items_before_section:
                        item_name = stored_row[0].strip()
                        result[initial_section][item_name] = {}
                        for i, header in enumerate(headers):
                            positions = header_positions[i]
                            value_found = ""
                            for pos in positions:
                                if pos < len(stored_row) and stored_row[pos].strip():
                                    value_found = stored_row[pos].strip()
                                    break
                            result[initial_section][item_name][header] = value_found
                items_before_section = []
            current_section = row[0].strip()
            result[current_section] = {}
            continue
        if row[0].strip():
            if current_section is None:
                items_before_section.append(row)
            else:
                item_name = row[0].strip()
                result[current_section][item_name] = {}
                for i, header in enumerate(headers):
                    positions = header_positions[i]
                    value_found = ""
                    for pos in positions:
                        if pos < len(row) and row[pos].strip():
                            value_found = row[pos].strip()
                            break
                    result[current_section][item_name][header] = value_found
    if current_section is None and items_before_section:
        # Check if we should create artificial section headers or treat as flat structure
        # If the first item has data in multiple columns, it's likely a data row, not a section header
        first_row = items_before_section[0]
        first_item_data_count = sum(1 for cell in first_row[1:] if cell.strip())

        # If first item has data in multiple columns, add them as top-level items
        # instead of creating artificial section headers
        if first_item_data_count > 1:
            # Add items before section as top-level items
            for stored_row in items_before_section:
                item_name = stored_row[0].strip()
                if item_name:  # Only process rows with names
                    result[item_name] = {}
                    for i, header in enumerate(headers):
                        positions = header_positions[i]
                        value_found = ""
                        for pos in positions:
                            if pos < len(stored_row) and stored_row[pos].strip():
                                value_found = stored_row[pos].strip()
                                break
                        if value_found:  # Only add non-empty values
                            result[item_name][header] = value_found
        else:
            # Original logic for creating section headers
            first_item = items_before_section[0][0].strip()
            initial_section = first_item
            result[initial_section] = {}

            for stored_row in items_before_section:
                item_name = stored_row[0].strip()
                result[initial_section][item_name] = {}
                for i, header in enumerate(headers):
                    positions = header_positions[i]
                    value_found = ""
                    for pos in positions:
                        if pos < len(stored_row) and stored_row[pos].strip():
                            value_found = stored_row[pos].strip()
                            break
                    result[initial_section][item_name][header] = value_found

    return result

def convert_subtotal_aggregated_table(matrix: List[List[str]]) -> Dict[str, Dict[str, Dict[str, str]]]:
    if len(matrix) < 2:
        return {}

    all_headers = [col.strip() for col in matrix[0]]
    headers = [h for h in all_headers if h]

    result = {}
    current_section = None

    for row in matrix[1:]:
        if not any(cell.strip() for cell in row):
            continue

        non_empty_count = sum(1 for cell in row[1:] if cell.strip())
        if row[0].strip() and non_empty_count <= 1:
            current_section = row[0].strip()
            result[current_section] = {}
            continue

        if row[0].strip() and current_section:
            item_name = row[0].strip()
            result[current_section][item_name] = {}

            for i, value in enumerate(row[1:]):
                if i < len(headers) and value.strip():
                    result[current_section][item_name][headers[i]] = value.strip()

    return result

def convert_and_save_table(filepath: str, output_dir: str) -> str:
    """
    Complete table conversion workflow: load → classify → convert → analyze → save.

    This function handles the entire conversion process for a single table:
    1. Loads table data from JSON file
    2. Classifies table structure pattern
    3. Converts to appropriate key-value format
    4. Analyzes for unicode symbols requiring replacement
    5. Saves converted table with metadata

    Args:
        filepath (str): Path to table JSON file
        output_dir (str): Directory to save converted table

    Returns:
        str: Classification result or "invalid" if processing failed
    """
    # Step 1: Load table data from file
    table_data = load_table_data(filepath)

    # Validate table data has required structure information
    if not table_data or "with_span" not in table_data:
        return "invalid"

    # Step 2: Extract table identifier from filename
    filename = os.path.basename(filepath)
    table_num_str = re.search(r'(\d+)', filename).group(1) if re.search(r'(\d+)', filename) else "unknown"

    # Step 3: Classify table structure to determine conversion strategy
    classification = classify_table_file(filepath)

    # Step 4: Extract caption for preservation in output
    caption = table_data.get("caption", None)

    # Step 5: Convert table to key-value JSON format based on classification
    converted_data = convert_to_key_value_json(table_data, classification)

    # Step 6: Analyze table content for unicode symbols requiring replacement
    matrix = build_expanded_matrix(table_data)
    symbols_found = scan_matrix_for_symbols(matrix) if matrix else []

    # Step 7: Create output structure with symbol metadata if needed
    if symbols_found:
        # Check if any symbols need semantic processing (context-aware replacement)
        has_other_category_symbols = any(s['category'] == 'other' for s in symbols_found)

        final_output = {
            "_comment_symbols_detected": f"Unicode symbols detected: {', '.join([s['symbol'] + ' (' + s['unicode_code'] + ' - ' + s['category'] + ')' for s in symbols_found])}",
            "_semantic_processing_needed": has_other_category_symbols,
            "caption": caption,
            "data": converted_data
        }
    else:
        # No symbols found - simple output structure
        final_output = {
            "caption": caption,
            "data": converted_data
        }

    output_filename = f"table_{table_num_str}_converted.json"
    output_path = os.path.join(output_dir, output_filename)

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(final_output, f, indent=2, ensure_ascii=False)

    return classification





def extract_table_with_span_lxml(table_el):
    rows = table_el.xpath(".//tr")
    table_data = []
    for row in rows:
        row_data = []
        for cell in row.xpath("./th|./td"):
            row_data.append({
                "tag": cell.tag,
                "text": ''.join(cell.itertext()).strip(),
                "rowspan": int(cell.get("rowspan", "1")),
                "colspan": int(cell.get("colspan", "1"))
            })
        table_data.append(row_data)
    return table_data

def extract_table_with_span_bs4(table):
    rows = table.find_all("tr")
    table_data = []
    for row in rows:
        row_data = []
        for cell in row.find_all(["th", "td"]):
            row_data.append({
                "tag": cell.name,
                "text": cell.get_text(strip=True),
                "rowspan": int(cell.get("rowspan", 1)),
                "colspan": int(cell.get("colspan", 1))
            })
        table_data.append(row_data)
    return table_data

def process_tables_with_lxml(html_content):
    from lxml import html, etree
    try:
        tree = html.fromstring(html_content)
        tables = tree.xpath("//table")
        output = []
        for idx, table in enumerate(tables, 1):
            caption_el = table.xpath("./caption")
            caption = caption_el[0].text.strip() if caption_el else None

            # Find the position of this table in the original HTML string
            table_html = etree.tostring(table, encoding='unicode', method='html')
            start_pos = html_content.find(table_html)
            end_pos = start_pos + len(table_html) if start_pos != -1 else -1

            # If exact match not found, try to find by table tag pattern
            if start_pos == -1:
                # Look for table opening tag pattern
                import re
                table_pattern = r'<table[^>]*>'
                matches = list(re.finditer(table_pattern, html_content, re.IGNORECASE))
                if idx <= len(matches):
                    start_pos = matches[idx-1].start()
                    # Find corresponding closing tag - look for the FIRST </table> after start
                    remaining_html = html_content[start_pos:]
                    table_end_pattern = r'</table>'
                    end_match = re.search(table_end_pattern, remaining_html, re.IGNORECASE)
                    if end_match:
                        end_pos = start_pos + end_match.end()

            # Double-check: ensure we're only capturing the table itself
            if start_pos != -1 and end_pos != -1:
                extracted_content = html_content[start_pos:end_pos]
                # Verify it ends with </table>
                if not extracted_content.lower().endswith('</table>'):
                    # Find the actual end of this table
                    table_close_pos = extracted_content.lower().rfind('</table>')
                    if table_close_pos != -1:
                        end_pos = start_pos + table_close_pos + 8  # 8 = len('</table>')

            output.append({
                "index": idx,
                "caption": caption,
                "start_position": start_pos,
                "end_position": end_pos,
                "with_span": extract_table_with_span_lxml(table)
            })
        return output
    except (etree.XMLSyntaxError, etree.ParserError, ValueError) as e:
        raise e

def process_tables_with_bs4(html_content):
    from bs4 import BeautifulSoup
    soup = BeautifulSoup(html_content, "html.parser")
    tables = soup.find_all("table")
    output = []
    for idx, table in enumerate(tables, 1):
        caption = table.caption.get_text(strip=True) if table.caption else None

        # Find the position of this table in the original HTML string
        table_html = str(table)
        start_pos = html_content.find(table_html)
        end_pos = start_pos + len(table_html) if start_pos != -1 else -1

        # If exact match not found, try to find by table tag pattern
        if start_pos == -1:
            import re
            table_pattern = r'<table[^>]*>'
            matches = list(re.finditer(table_pattern, html_content, re.IGNORECASE))
            if idx <= len(matches):
                start_pos = matches[idx-1].start()
                # Find corresponding closing tag - look for the FIRST </table> after start
                remaining_html = html_content[start_pos:]
                table_end_pattern = r'</table>'
                end_match = re.search(table_end_pattern, remaining_html, re.IGNORECASE)
                if end_match:
                    end_pos = start_pos + end_match.end()

        # Double-check: ensure we're only capturing the table itself
        if start_pos != -1 and end_pos != -1:
            extracted_content = html_content[start_pos:end_pos]
            # Verify it ends with </table>
            if not extracted_content.lower().endswith('</table>'):
                # Find the actual end of this table
                table_close_pos = extracted_content.lower().rfind('</table>')
                if table_close_pos != -1:
                    end_pos = start_pos + table_close_pos + 8  # 8 = len('</table>')

        output.append({
            "index": idx,
            "caption": caption,
            "start_position": start_pos,
            "end_position": end_pos,
            "with_span": extract_table_with_span_bs4(table)
        })
    return output

def process_tables(html_content: str) -> List[Dict[str, Any]]:
    """
    Extract tables from HTML content with fallback parsing strategy.

    Uses lxml as primary parser for speed and accuracy, falls back to
    BeautifulSoup if lxml fails due to malformed HTML.

    Args:
        html_content (str): HTML string containing table elements

    Returns:
        List[Dict[str, Any]]: List of table dictionaries with metadata:
            - index: Table position in document
            - caption: Table caption text if present
            - start_position: Character position where table starts
            - end_position: Character position where table ends
            - with_span: Table data with rowspan/colspan information
    """
    try:
        # Primary parsing strategy: Use lxml for better performance
        return process_tables_with_lxml(html_content)
    except Exception:
        # Fallback parsing strategy: Use BeautifulSoup for malformed HTML
        return process_tables_with_bs4(html_content)



def remove_whitespace_and_blank_lines(text: str) -> str:
    """
    Remove all blank lines and unnecessary whitespaces for clean single-line output.

    This function performs comprehensive whitespace cleanup:
    1. Splits text into individual lines
    2. Strips leading/trailing whitespace from each line
    3. Removes completely empty lines
    4. Joins remaining content into single continuous line

    Args:
        text (str): Input text potentially containing multiple lines and excess whitespace

    Returns:
        str: Clean single-line text with all unnecessary whitespace removed
    """
    # Step 1: Split text into lines and clean each line
    # Remove empty lines and strip whitespace from remaining lines
    lines = [line.strip() for line in text.split('\n') if line.strip()]

    # Step 2: Join all content into single continuous line
    # No separators between lines to create compact output
    return ''.join(lines)

def remove_unnecessary_brackets(data: Any) -> Any:
    """Remove unnecessary bracket pairs from table data"""
    # If it's a list with only one element and that element is a dict or another meaningful structure
    if isinstance(data, list):
        if len(data) == 1:
            # Single item list - unwrap it
            return remove_unnecessary_brackets(data[0])
        elif len(data) == 0:
            # Empty list - return empty dict instead
            return {}
        else:
            # Multiple items - keep as list but clean each item
            return [remove_unnecessary_brackets(item) for item in data]

    # If it's a dict, recursively clean its values
    elif isinstance(data, dict):
        cleaned_dict = {}
        for key, value in data.items():
            cleaned_dict[key] = remove_unnecessary_brackets(value)
        return cleaned_dict

    # For other types (strings, numbers, etc.), return as-is
    else:
        return data



def correct_tables(input_string: str) -> str:
    """
    Core table processing function with individual table processing workflow.

    Key features:
    - Processes tables one by one: detect → process → replace → move to next
    - Supports nested dictionary structures of any depth
    - Preserves all non-table content exactly as-is
    - Caption preservation in <caption> tags
    - Symbol replacement for unicode symbols
    - Deletes temporary files after each table processing

    Args:
        input_string (str): Input string containing HTML tables or dict format
                           Can be plain HTML or nested dictionary structure

    Returns:
        str: Processed string with tables converted to JSON format
             Maintains original input structure while updating table content
    """
    try:
        # Step 1: Parse input and discover content structure
        # Try to parse as Python dictionary first (handles di_parser_output.txt format)
        try:
            import ast
            data = ast.literal_eval(input_string)
            html_content = ""
            language = "en"
            original_structure = data

            # Recursive function to discover content in nested dictionary structures
            def find_content_and_language(obj, path=""):
                """Recursively search for 'content' key in nested dictionaries"""
                nonlocal html_content, language
                if isinstance(obj, dict):
                    if "content" in obj:
                        # Found content key - extract HTML and language
                        html_content = obj.get("content", "")
                        language = obj.get("language", "en")
                        return True
                    else:
                        # Continue recursive search in nested dictionaries
                        for key, value in obj.items():
                            if find_content_and_language(value, f"{path}.{key}" if path else key):
                                return True
                return False

            if not find_content_and_language(data):
                # Fallback: if no content found, treat the whole thing as content
                html_content = str(data)

        except (ValueError, SyntaxError):
            # If parsing as dict fails, treat as plain HTML
            html_content = input_string
            language = "en"
            original_structure = None

        # Step 2: Extract all tables from HTML content
        tables = process_tables(html_content)
        total_tables = len(tables)

        # Start with the original HTML content
        modified_content = html_content

        # Step 3: Process each table individually and replace in string
        # Process in reverse order to maintain position accuracy
        for table in reversed(tables):
            start_pos = table.get('start_position', -1)
            end_pos = table.get('end_position', -1)

            if start_pos == -1 or end_pos == -1:
                continue  # Skip tables without valid positions

            try:
                # Step 3a: Process table directly without temporary files
                # Validate table data has required structure information
                if not table or "with_span" not in table:
                    continue  # Skip invalid tables

                # Step 3b: Classify table structure to determine conversion strategy
                # Use the existing classification logic directly on table data
                matrix = build_expanded_matrix(table)
                if not matrix or len(matrix) < 1:
                    continue  # Skip empty tables

                # Determine classification based on table structure
                classification = classify_table_from_data(table)

                # Step 3c: Convert table to key-value JSON format based on classification
                converted_data = convert_to_key_value_json(table, classification)
                caption = table.get('caption', None)

                # Step 3d: Analyze table content for unicode symbols requiring replacement
                symbols_found = scan_matrix_for_symbols(matrix) if matrix else []

                # Step 3e: Process symbols if found
                if symbols_found:
                    # Check if any symbols need semantic processing (context-aware replacement)
                    has_other_category_symbols = any(s['category'] == 'other' for s in symbols_found)

                    if has_other_category_symbols:
                        context_map = analyze_json_structure_for_context(converted_data)
                        processed_data = process_json_data_with_symbol_replacement(converted_data, context_map)
                    else:
                        processed_data = process_json_data_with_symbol_replacement(converted_data, {})

                    converted_data = processed_data

                # Step 3f: Create final table data structure and replacement content
                if converted_data:
                    cleaned_data = remove_unnecessary_brackets(converted_data)
                    table_json = json.dumps(cleaned_data, ensure_ascii=False, separators=(',', ':'))

                    if caption:
                        replacement_content = f"<table><caption>{caption}</caption>{table_json}</table>"
                    else:
                        replacement_content = f"<table>{table_json}</table>"

                    # Step 3g: Replace the original table with processed version in the string
                    modified_content = (modified_content[:start_pos] +
                                      replacement_content +
                                      modified_content[end_pos:])

            except Exception:
                pass  # Silently continue with next table

        # Step 4: Clean up the final content
        all_content = remove_whitespace_and_blank_lines(modified_content)

        # Step 5: Create the final output string preserving original structure
        if original_structure is not None:
            # Reconstruct the original structure with updated content
            def update_content_in_structure(obj, new_content):
                if isinstance(obj, dict):
                    if "content" in obj:
                        # Found the content key, update it
                        updated_obj = obj.copy()
                        updated_obj["content"] = new_content
                        return updated_obj
                    else:
                        # Recursively search and update nested dictionaries
                        updated_obj = {}
                        for key, value in obj.items():
                            updated_value = update_content_in_structure(value, new_content)
                            if updated_value != value:  # Content was updated in this branch
                                updated_obj[key] = updated_value
                                # Copy other keys as-is
                                for other_key, other_value in obj.items():
                                    if other_key != key:
                                        updated_obj[other_key] = other_value
                                return updated_obj
                            else:
                                updated_obj[key] = value
                        return updated_obj
                return obj

            updated_structure = update_content_in_structure(original_structure, all_content)
            final_output = str(updated_structure)
        else:
            # If no original structure, just return the processed content as-is
            final_output = all_content

        # Step 6: Print success message and save output
        print(f"{total_tables} tables found and have been processed and corrected")

        # Save as .txt file with specified format
        with open("all_tables_converted.txt", 'w', encoding='utf-8') as f:
            f.write(final_output)

        return final_output

    except Exception as e:
        print(f"Error in correct_tables: {str(e)}")
        return None

if __name__ == "__main__":
    # Example usage - you can replace this with your actual string content
    example_html ="""{'content': '# Design and build accessible PDF tables Sample tables\n\n\n<table>\n<caption>Table 1</caption>\n<tr>\n<th>Column header (TH)</th>\n<th>Column header (TH)</th>\n<th>Column header (TH)</th>\n</tr>\n<tr>\n<td>Row header (TH)</td>\n<td>Data cell (TD)</td>\n<td>Data cell (TD)</td>\n</tr>\n<tr>\n<td>Row header(TH)</td>\n<td>Data cell (TD)</td>\n<td>Data cell (TD)</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 2: example of footnotes referenced from within a table</caption>\n<tr>\n<th colspan="2">Expenditure by function £ million</th>\n<th>2009/10</th>\n<th>2010/11 1</th>\n</tr>\n<tr>\n<td rowspan="3">Policy functions</td>\n<td>Financial</td>\n<td>22.5</td>\n<td>30.57</td>\n</tr>\n<tr>\n<td>Information 2</td>\n<td>10.2</td>\n<td>14.8</td>\n</tr>\n<tr>\n<td>Contingency</td>\n<td>2.6</td>\n<td>1.2</td>\n</tr>\n<tr>\n<td rowspan="4">Remunerated functions</td>\n<td>Agency services 3</td>\n<td>44.7</td>\n<td>35.91</td>\n</tr>\n<tr>\n<td>Payments</td>\n<td>22.41</td>\n<td>19.88</td>\n</tr>\n<tr>\n<td>Banking</td>\n<td>22.90</td>\n<td>44.23</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>12.69</td>\n<td>10.32</td>\n</tr>\n</table>\n\n(1) Provisional total as of publication date.\n\n(2) Costs associated with on-going information programmes.\n\n(3) From the management accounts, net of recoveries, including interest charges.\n\n\n<table>\n<caption>Table 3: "film credits" style layout</caption>\n<tr>\n<td>Main character</td>\n<td>Daniel Radcliffe</td>\n</tr>\n<tr>\n<td>Sidekick 1</td>\n<td>Rupert Grint</td>\n</tr>\n<tr>\n<td>Sidekick 2</td>\n<td>Emma Watson</td>\n</tr>\n<tr>\n<td>Lovable ogre</td>\n<td>Robbie Coltrane</td>\n</tr>\n<tr>\n<td>Professor</td>\n<td>Maggie Smith</td>\n</tr>\n<tr>\n<td>Headmaster</td>\n<td>Richard Harris</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 1**\n\n\n<table>\n<caption>Table 4: table 3 with column headers added</caption>\n<tr>\n<th>Role</th>\n<th>Actor</th>\n</tr>\n<tr>\n<td>Main character</td>\n<td>Daniel Radcliffe</td>\n</tr>\n<tr>\n<td>Sidekick 1</td>\n<td>Rupert Grint</td>\n</tr>\n<tr>\n<td>Sidekick 2</td>\n<td>Emma Watson</td>\n</tr>\n<tr>\n<td>Lovable ogre</td>\n<td>Robbie Coltrane</td>\n</tr>\n<tr>\n<td>Professor</td>\n<td>Maggie Smith</td>\n</tr>\n<tr>\n<td>Headmaster</td>\n<td>Richard Harris</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 5: year-end financial statement (£, thousands)</caption>\n<tr>\n<th></th>\n<th>2010</th>\n<th>2009</th>\n<th>2008</th>\n</tr>\n<tr>\n<td>Non-current assets</td>\n<td></td>\n<td colspan="2"></td>\n</tr>\n<tr>\n<td>Property</td>\n<td>345</td>\n<td>445</td>\n<td>222</td>\n</tr>\n<tr>\n<td>Investment</td>\n<td>567</td>\n<td>654</td>\n<td>423</td>\n</tr>\n<tr>\n<td>Intangibles</td>\n<td>423</td>\n<td>123</td>\n<td>453</td>\n</tr>\n<tr>\n<td>Current assets</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Trade and other receivables</td>\n<td>435</td>\n<td>634</td>\n<td>231</td>\n</tr>\n<tr>\n<td>Cash and cash equivalents</td>\n<td>524</td>\n<td>123</td>\n<td>482</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>223</td>\n<td>211</td>\n<td>254</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 6: a table with a more serious headings problem</caption>\n<tr>\n<th>Rainfall (inches)</th>\n<th>Americas</th>\n<th>Asia</th>\n<th>Europe</th>\n<th>Africa</th>\n</tr>\n<tr>\n<td colspan="5">2010</td>\n</tr>\n<tr>\n<td>Average</td>\n<td>104</td>\n<td>201</td>\n<td>193</td>\n<td>144</td>\n</tr>\n<tr>\n<td>24 hour high</td>\n<td>15</td>\n<td>26</td>\n<td>27</td>\n<td>18</td>\n</tr>\n<tr>\n<td>12 hour high</td>\n<td>9</td>\n<td>10</td>\n<td>11</td>\n<td>12</td>\n</tr>\n<tr>\n<td colspan="5">2009</td>\n</tr>\n<tr>\n<td>Average</td>\n<td>133</td>\n<td>244</td>\n<td>155</td>\n<td>166</td>\n</tr>\n<tr>\n<td>24 hour high</td>\n<td>27</td>\n<td>28</td>\n<td>29</td>\n<td>20</td>\n</tr>\n<tr>\n<td>12 hour high</td>\n<td>11</td>\n<td>12</td>\n<td>13</td>\n<td>16</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 2**\n\n\n<table>\n<caption>Table 7: year-end statement, non-current assets (£, thousands)</caption>\n<tr>\n<th>Non-current assets</th>\n<th>2010</th>\n<th>2009</th>\n<th>2008</th>\n</tr>\n<tr>\n<td>Property</td>\n<td>345</td>\n<td>445</td>\n<td>222</td>\n</tr>\n<tr>\n<td>Investment</td>\n<td>567</td>\n<td>654</td>\n<td>423</td>\n</tr>\n<tr>\n<td>Intangibles</td>\n<td>423</td>\n<td>123</td>\n<td>453</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 8: year-end statement, current assets (£, thousands)</caption>\n<tr>\n<th>Current assets</th>\n<th>2010</th>\n<th>2009</th>\n<th>2008</th>\n</tr>\n<tr>\n<td>Trade and other receivables</td>\n<td>435</td>\n<td>634</td>\n<td>231</td>\n</tr>\n<tr>\n<td>Cash and cash equivalents</td>\n<td>524</td>\n<td>123</td>\n<td>482</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>223</td>\n<td>211</td>\n<td>254</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 9: rainfall by continent, 2009</caption>\n<tr>\n<th>Rainfall (inches)</th>\n<th>Americas</th>\n<th>Asia</th>\n<th>Europe</th>\n<th>Africa</th>\n</tr>\n<tr>\n<td>Average</td>\n<td>133</td>\n<td>244</td>\n<td>155</td>\n<td>166</td>\n</tr>\n<tr>\n<td>24 hour high</td>\n<td>27</td>\n<td>28</td>\n<td>29</td>\n<td>20</td>\n</tr>\n<tr>\n<td>12 hour high</td>\n<td>11</td>\n<td>12</td>\n<td>13</td>\n<td>16</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 3**\n\n\n<table>\n<caption>Table 10: self-contained year-end statement (£, thousands) (multiple layout problems)</caption>\n<tr>\n<th></th>\n<th colspan="2">2011</th>\n<th colspan="2">2010 restated</th>\n</tr>\n<tr>\n<td>General income</td>\n<td></td>\n<td>250,000</td>\n<td></td>\n<td>200,000</td>\n</tr>\n<tr>\n<td>Increase in value, WIP</td>\n<td></td>\n<td>15,000</td>\n<td></td>\n<td>30,000</td>\n</tr>\n<tr>\n<td></td>\n<td></td>\n<td>265,000</td>\n<td></td>\n<td>230,000</td>\n</tr>\n<tr>\n<td>Administrative costs</td>\n<td></td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Staff costs</td>\n<td>(200,000)</td>\n<td></td>\n<td>(150,000)</td>\n<td></td>\n</tr>\n<tr>\n<td>Early departures</td>\n<td>(10,000)</td>\n<td></td>\n<td>(20,000)</td>\n<td></td>\n</tr>\n<tr>\n<td>Other</td>\n<td>(25,000)</td>\n<td></td>\n<td>(10,000)</td>\n<td></td>\n</tr>\n<tr>\n<td>Depreciation</td>\n<td>(10,000)</td>\n<td></td>\n<td>(10,000)</td>\n<td></td>\n</tr>\n<tr>\n<td>Programme costs</td>\n<td></td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Impairment loss</td>\n<td>(10,000)</td>\n<td></td>\n<td>(5,000)</td>\n<td></td>\n</tr>\n<tr>\n<td>Other</td>\n<td>(5,000)</td>\n<td></td>\n<td>(5,000)</td>\n<td></td>\n</tr>\n<tr>\n<td></td>\n<td>(260,000)</td>\n<td></td>\n<td>(200,000)</td>\n<td></td>\n</tr>\n<tr>\n<td>Surplus</td>\n<td></td>\n<td>5,000</td>\n<td></td>\n<td>30,000</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 11: self-contained year-end statement (£, thousands) (multiple problems resolved)</caption>\n<tr>\n<th colspan="2"></th>\n<th>2011</th>\n<th>2010 restated</th>\n</tr>\n<tr>\n<td rowspan="3">Income</td>\n<td>General income</td>\n<td>250,000</td>\n<td>200,000</td>\n</tr>\n<tr>\n<td>Increase in value</td>\n<td>15,000</td>\n<td>30,000</td>\n</tr>\n<tr>\n<td>Total income</td>\n<td>265,000</td>\n<td>230,000</td>\n</tr>\n<tr>\n<td rowspan="4">Administrative costs</td>\n<td>Staff costs</td>\n<td>(200,000)</td>\n<td>(150,000)</td>\n</tr>\n<tr>\n<td>Early departures</td>\n<td>(10,000)</td>\n<td>(20,000)</td>\n</tr>\n<tr>\n<td>Other operating costs</td>\n<td>(25,000)</td>\n<td>(10,000)</td>\n</tr>\n<tr>\n<td>Depreciation</td>\n<td>(10,000)</td>\n<td>(10,000)</td>\n</tr>\n<tr>\n<td rowspan="3">Programme costs</td>\n<td>Impairment loss</td>\n<td>(10,000)</td>\n<td>(5,000)</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>(5,000)</td>\n<td>(5,000)</td>\n</tr>\n<tr>\n<td>Total costs</td>\n<td>(260,000)</td>\n<td>(200,000)</td>\n</tr>\n<tr>\n<td colspan="2">Surplus</td>\n<td>5,000</td>\n<td>30,000</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 4**\n\n\n<table>\n<caption>Table 12: merged data cells are not recommended</caption>\n<tr>\n<th></th>\n<th colspan="2">2008</th>\n<th colspan="2">2009</th>\n</tr>\n<tr>\n<th>Name</th>\n<th>Yes</th>\n<th>No</th>\n<th>Yes</th>\n<th>No</th>\n</tr>\n<tr>\n<td>Bob</td>\n<td>2</td>\n<td>5</td>\n<td>6</td>\n<td>7</td>\n</tr>\n<tr>\n<td>Sue</td>\n<td>3</td>\n<td>8</td>\n<td>4</td>\n<td>7</td>\n</tr>\n<tr>\n<td>Sam</td>\n<td colspan="2">[data relating to both columns in a single cell spanning both]</td>\n<td colspan="2">[data relating to both columns in a single cell spanning both]</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 13: use of graphic symbols</caption>\n<tr>\n<th>Question</th>\n<th>Respondent A</th>\n<th>Respondent B</th>\n<th>Respondent C</th>\n</tr>\n<tr>\n<td>Are you a UK citizen?</td>\n<td>☒</td>\n<td>☒</td>\n<td>☒</td>\n</tr>\n<tr>\n<td>Are you currently employed?</td>\n<td>☒</td>\n<td>☒</td>\n<td>☒</td>\n</tr>\n<tr>\n<td>Do you have a driving licence?</td>\n<td>☒</td>\n<td>☒</td>\n<td>☒</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 14: symbols replaced by real text</caption>\n<tr>\n<th>Question</th>\n<th>Respondent A</th>\n<th>Respondent B</th>\n<th>Respondent C</th>\n</tr>\n<tr>\n<td>Are you a UK citizen?</td>\n<td>No</td>\n<td>Yes</td>\n<td>No</td>\n</tr>\n<tr>\n<td>Are you currently employed?</td>\n<td>Yes</td>\n<td>No</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td>Do you have a driving licence?</td>\n<td>No</td>\n<td>No</td>\n<td>Yes</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 15: courses offered by Institution X. A = Bachelor of Science, B = Bachelor of Arts, C = Masters, D = Doctorate, E = Diploma</caption>\n<tr>\n<th></th>\n<th>2006</th>\n<th>2007</th>\n<th>2008</th>\n<th>2009</th>\n</tr>\n<tr>\n<td>Economics</td>\n<td>A, B</td>\n<td>A, C</td>\n<td>A, B</td>\n<td>A, C</td>\n</tr>\n<tr>\n<td>International relations</td>\n<td>A, E</td>\n<td>A, E</td>\n<td>A, B</td>\n<td>A, E</td>\n</tr>\n<tr>\n<td>Philosophy</td>\n<td>A</td>\n<td>A</td>\n<td>A</td>\n<td>A, D</td>\n</tr>\n<tr>\n<td>Politics</td>\n<td>A, D</td>\n<td>A, D</td>\n<td>A, D</td>\n<td>A</td>\n</tr>\n<tr>\n<td>Mathematics</td>\n<td>B, C</td>\n<td>B</td>\n<td>A, E</td>\n<td>A, B</td>\n</tr>\n<tr>\n<td>English</td>\n<td>A, C</td>\n<td>A, B</td>\n<td>A,B</td>\n<td>C</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 5**\n\n\n<table>\n<caption>Table 16: Masters courses offered by Institution X</caption>\n<tr>\n<th></th>\n<th>2006</th>\n<th>2007</th>\n<th>2008</th>\n<th>2009</th>\n</tr>\n<tr>\n<td>Economics</td>\n<td>No</td>\n<td>Yes</td>\n<td>Yes</td>\n<td>Yes</td>\n</tr>\n<tr>\n<td>International relations</td>\n<td>No</td>\n<td>No</td>\n<td>No</td>\n<td>No</td>\n</tr>\n<tr>\n<td>Philosophy</td>\n<td>No</td>\n<td>No</td>\n<td>No</td>\n<td>No</td>\n</tr>\n<tr>\n<td>Politics</td>\n<td>No</td>\n<td>No</td>\n<td>No</td>\n<td>No</td>\n</tr>\n<tr>\n<td>Mathematics</td>\n<td>Yes</td>\n<td>No</td>\n<td>No</td>\n<td>No</td>\n</tr>\n<tr>\n<td>English</td>\n<td>Yes</td>\n<td>Yes</td>\n<td>Yes</td>\n<td>Yes</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 17: accounts, 2011 (£, thousands)</caption>\n<tr>\n<th colspan="2">Accounting item</th>\n<th>2011</th>\n</tr>\n<tr>\n<td rowspan="3">Income</td>\n<td>General income</td>\n<td>200,000</td>\n</tr>\n<tr>\n<td>Increase in value, WIP</td>\n<td>30,000</td>\n</tr>\n<tr>\n<td>Income subtotal</td>\n<td>230,000</td>\n</tr>\n<tr>\n<td rowspan="4">Administrative costs</td>\n<td>Staff</td>\n<td>150,000</td>\n</tr>\n<tr>\n<td>Early departures</td>\n<td>20,000</td>\n</tr>\n<tr>\n<td>Other operating costs</td>\n<td>10,000</td>\n</tr>\n<tr>\n<td>Depreciation</td>\n<td>10,000</td>\n</tr>\n<tr>\n<td rowspan="2">Programme costs</td>\n<td>Impairment loss</td>\n<td>10,000</td>\n</tr>\n<tr>\n<td>Costs subtotal</td>\n<td>200,000</td>\n</tr>\n<tr>\n<td colspan="2">Balance</td>\n<td>30,000</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 18: accounts, 2011 (£, thousands)</caption>\n<tr>\n<th colspan="2">Accounting item</th>\n<th>2011</th>\n</tr>\n<tr>\n<td rowspan="3">Income</td>\n<td>General income</td>\n<td>200,000</td>\n</tr>\n<tr>\n<td>Increase in value, WIP</td>\n<td>30,000</td>\n</tr>\n<tr>\n<td>Income subtotal</td>\n<td>230,000</td>\n</tr>\n<tr>\n<td rowspan="4">Administrative costs</td>\n<td>Staff</td>\n<td>(150,000)</td>\n</tr>\n<tr>\n<td>Early departures</td>\n<td>(20,000)</td>\n</tr>\n<tr>\n<td>Other operating costs</td>\n<td>(10,000)</td>\n</tr>\n<tr>\n<td>Depreciation</td>\n<td>(10,000)</td>\n</tr>\n<tr>\n<td rowspan="2">Programme costs</td>\n<td>Impairment loss</td>\n<td>(10,000)</td>\n</tr>\n<tr>\n<td>Costs subtotal</td>\n<td>(200,000)</td>\n</tr>\n<tr>\n<td colspan="2">Balance</td>\n<td>30,000</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 6**\n\n\n<table>\n<caption>Table 19: Human Development Index (HDI) trends, 1980 to 2010. Source: Barro-Lee March, 2010</caption>\n<tr>\n<th>Country</th>\n<th>1980</th>\n<th>1990</th>\n<th>2000</th>\n<th>2010</th>\n</tr>\n<tr>\n<td>Afghanistan</td>\n<td>0.78</td>\n<td>1.48</td>\n<td>2.16</td>\n<td>3.33</td>\n</tr>\n<tr>\n<td>Albania</td>\n<td>8.89</td>\n<td>9.67</td>\n<td>9.89</td>\n<td>10.38</td>\n</tr>\n<tr>\n<td>Algeria</td>\n<td>4.74</td>\n<td>3.33</td>\n<td>5.50</td>\n<td>7.24</td>\n</tr>\n<tr>\n<td>Andorra</td>\n<td>4.98</td>\n<td>5.63</td>\n<td>9.09</td>\n<td>10.35</td>\n</tr>\n<tr>\n<td>Angola</td>\n<td>-</td>\n<td>-</td>\n<td>4.42</td>\n<td>4.42</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 20: footnotes referenced from within a table</caption>\n<tr>\n<th colspan="2">Expenditure by function £million</th>\n<th>2009/10</th>\n<th>2010/11 1</th>\n</tr>\n<tr>\n<td rowspan="3">Policy functions</td>\n<td>Financial</td>\n<td>22.5</td>\n<td>30.57</td>\n</tr>\n<tr>\n<td>Information 2</td>\n<td>10.2</td>\n<td>14.8</td>\n</tr>\n<tr>\n<td>Contingency</td>\n<td>2.6</td>\n<td>1.2</td>\n</tr>\n<tr>\n<td rowspan="4">Remunerated functions</td>\n<td>Agency services 3</td>\n<td>44.7</td>\n<td>35.91</td>\n</tr>\n<tr>\n<td>Payments</td>\n<td>22.41</td>\n<td>19.88</td>\n</tr>\n<tr>\n<td>Banking</td>\n<td>22.90</td>\n<td>44.23</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>12.69</td>\n<td>10.32</td>\n</tr>\n</table>\n\n(1) Provisional total as of publication date.\n\n(2) Costs associated with on-going information programmes.\n\n(3) From the management accounts, net of recoveries and including interest charges\n\n\n<!-- PageBreak -->\n\n\n\n**Page 7**\n\n\n<table>\n<caption>Table 21: footnotes replaced by additional table summary text</caption>\n<tr>\n<th colspan="2">Expenditure by function £million</th>\n<th>2009/10</th>\n<th>2010/11</th>\n</tr>\n<tr>\n<td rowspan="3">Policy functions</td>\n<td>Financial</td>\n<td>22.5</td>\n<td>30.57</td>\n</tr>\n<tr>\n<td>Information</td>\n<td>10.2</td>\n<td>14.8</td>\n</tr>\n<tr>\n<td>Contingency</td>\n<td>2.6</td>\n<td>1.2</td>\n</tr>\n<tr>\n<td rowspan="4">Remunerated functions</td>\n<td>Agency services</td>\n<td>44.7</td>\n<td>35.91</td>\n</tr>\n<tr>\n<td>Payments</td>\n<td>22.41</td>\n<td>19.88</td>\n</tr>\n<tr>\n<td>Banking</td>\n<td>22.90</td>\n<td>44.23</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>12.69</td>\n<td>10.32</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 22: referencing multiple endnotes from within a table</caption>\n<tr>\n<th>Expenditure £m</th>\n<th>Notes (Notes located on page [n])</th>\n<th>2010</th>\n<th>2011</th>\n</tr>\n<tr>\n<td>Information</td>\n<td>1</td>\n<td>10.2</td>\n<td>14.8</td>\n</tr>\n<tr>\n<td>Contingency</td>\n<td></td>\n<td>2.6</td>\n<td>1.2</td>\n</tr>\n<tr>\n<td>Payments</td>\n<td>3</td>\n<td>22.41</td>\n<td>19.88</td>\n</tr>\n<tr>\n<td>Banking services</td>\n<td>4</td>\n<td>22.90</td>\n<td>44.23</td>\n</tr>\n<tr>\n<td>Interest</td>\n<td></td>\n<td>0.23</td>\n<td>0.10</td>\n</tr>\n<tr>\n<td>Dividends</td>\n<td>23</td>\n<td>2.5</td>\n<td>3.68</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>9</td>\n<td>12.69</td>\n<td>10.32</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 23: simulated table created using tabs and containing no structure</caption>\n<tr>\n<th></th>\n<th colspan="2">2008</th>\n<th colspan="2">2009</th>\n</tr>\n<tr>\n<th>Name</th>\n<th>Entered</th>\n<th>Completed</th>\n<th>Entered</th>\n<th>Completed</th>\n</tr>\n<tr>\n<td>Bob</td>\n<td>22</td>\n<td>21</td>\n<td>20</td>\n<td>19</td>\n</tr>\n<tr>\n<td>Sue</td>\n<td>44</td>\n<td>12</td>\n<td>12</td>\n<td>10</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 8**\n\n\n<table>\n<caption>Table 24: year-end financial statement (£, thousands)</caption>\n<tr>\n<th></th>\n<th>2010</th>\n<th>2009</th>\n<th>2008</th>\n</tr>\n<tr>\n<td>Non-current assets</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Buildings</td>\n<td>345</td>\n<td>445</td>\n<td>222</td>\n</tr>\n<tr>\n<td>Investment</td>\n<td>567</td>\n<td>654</td>\n<td>423</td>\n</tr>\n<tr>\n<td>Intangibles</td>\n<td>423</td>\n<td>123</td>\n<td>453</td>\n</tr>\n<tr>\n<td>Current assets</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Trade</td>\n<td>435</td>\n<td>634</td>\n<td>231</td>\n</tr>\n<tr>\n<td>Cash</td>\n<td>524</td>\n<td>123</td>\n<td>482</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>223</td>\n<td>211</td>\n<td>254</td>\n</tr>\n<tr>\n<td>Current liabilities</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Trade liabilities</td>\n<td>154</td>\n<td>125</td>\n<td>421</td>\n</tr>\n<tr>\n<td>Financial debt</td>\n<td>231</td>\n<td>474</td>\n<td>572</td>\n</tr>\n<tr>\n<td>Provisions</td>\n<td>111</td>\n<td>312</td>\n<td>347</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 25: setting column and row scope via the tags panel</caption>\n<tr>\n<th></th>\n<th colspan="2">2008</th>\n<th colspan="2">2009</th>\n</tr>\n<tr>\n<th>Name</th>\n<th>Entered</th>\n<th>Won</th>\n<th>Entered</th>\n<th>Won</th>\n</tr>\n<tr>\n<td>Bob</td>\n<td>22</td>\n<td>21</td>\n<td>20</td>\n<td>19</td>\n</tr>\n<tr>\n<td>Sue</td>\n<td>44</td>\n<td>12</td>\n<td>12</td>\n<td>10</td>\n</tr>\n<tr>\n<td>Sam</td>\n<td>16</td>\n<td>4</td>\n<td>45</td>\n<td>30</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 26: courses offered by Institution X. A = Bachelor of Science, B = Bachelor of Arts, C = Masters, D = Doctorate, E = Diploma</caption>\n<tr>\n<th></th>\n<th>2006</th>\n<th>2007</th>\n<th>2008</th>\n<th>2009</th>\n</tr>\n<tr>\n<td>Economics</td>\n<td>A, B</td>\n<td>A, C</td>\n<td>A, C</td>\n<td>A, C</td>\n</tr>\n<tr>\n<td>International relations</td>\n<td>A, E</td>\n<td>A, E</td>\n<td>A, B</td>\n<td>A, B</td>\n</tr>\n<tr>\n<td>Philosophy</td>\n<td>A</td>\n<td>A</td>\n<td>A</td>\n<td>A</td>\n</tr>\n<tr>\n<td>Politics</td>\n<td>A, D</td>\n<td>A, D</td>\n<td>A, B</td>\n<td>A</td>\n</tr>\n<tr>\n<td>Mathematics</td>\n<td>B, C</td>\n<td>B</td>\n<td>A, B</td>\n<td>A, B</td>\n</tr>\n<tr>\n<td>English</td>\n<td>A, C</td>\n<td>A, B</td>\n<td>A,B</td>\n<td>A, C</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 9**\n\n\n<table>\n<caption>Table 27: "table" with columns simulated by using tab stops</caption>\n<tr>\n<th>Name</th>\n<th>Apples</th>\n<th>Pears</th>\n</tr>\n<tr>\n<td>Bob Scott</td>\n<td>20</td>\n<td>25</td>\n</tr>\n<tr>\n<td>Susan. P. Arnold-Jones, BA, FRSA, MD</td>\n<td>24</td>\n<td>15</td>\n</tr>\n<tr>\n<td>Sam Holder-Dickinson</td>\n<td>14</td>\n<td>10</td>\n</tr>\n</table>\n\n\n<table>\n<caption>Table 28: year-end financial table (£, thousands) - headings problem revisited</caption>\n<tr>\n<th></th>\n<th>2010</th>\n<th>2009</th>\n<th>2008</th>\n</tr>\n<tr>\n<td>Non-current assets</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Buildings</td>\n<td>345</td>\n<td>445</td>\n<td>222</td>\n</tr>\n<tr>\n<td>Investment</td>\n<td>567</td>\n<td>654</td>\n<td>423</td>\n</tr>\n<tr>\n<td>Intangibles</td>\n<td>423</td>\n<td>123</td>\n<td>453</td>\n</tr>\n<tr>\n<td>Current assets</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Trade</td>\n<td>435</td>\n<td>634</td>\n<td>231</td>\n</tr>\n<tr>\n<td>Cash</td>\n<td>524</td>\n<td>123</td>\n<td>482</td>\n</tr>\n<tr>\n<td>Other</td>\n<td>223</td>\n<td>211</td>\n<td>254</td>\n</tr>\n<tr>\n<td>Current liabilities</td>\n<td></td>\n<td></td>\n<td></td>\n</tr>\n<tr>\n<td>Trade liabilities</td>\n<td>154</td>\n<td>125</td>\n<td>421</td>\n</tr>\n<tr>\n<td>Financial debt</td>\n<td>231</td>\n<td>474</td>\n<td>572</td>\n</tr>\n<tr>\n<td>Provisions</td>\n<td>111</td>\n<td>312</td>\n<td>347</td>\n</tr>\n</table>\n\n\n<!-- PageBreak -->\n\n\n\n**Page 10**\n\n\n<table>\n<caption>Table 29: multiple headers attributes for each data cell</caption>\n<tr>\n<th></th>\n<th>South America</th>\n<th>Asia</th>\n<th>Africa</th>\n<th>Australia</th>\n</tr>\n<tr>\n<td colspan="5">2010</td>\n</tr>\n<tr>\n<td>Highest average</td>\n<td>523.6</td>\n<td>467.4</td>\n<td>405.0</td>\n<td>340.5</td>\n</tr>\n<tr>\n<td>Highest in 24 hours</td>\n<td>73.1</td>\n<td>54.1</td>\n<td>27.2</td>\n<td>66.3</td>\n</tr>\n<tr>\n<td>Highest in 12 hours</td>\n<td>42.4</td>\n<td>30.1</td>\n<td>15.9</td>\n<td>40.3</td>\n</tr>\n<tr>\n<td colspan="5">2009</td>\n</tr>\n<tr>\n<td>Highest average</td>\n<td>487.7</td>\n<td>453.6</td>\n<td>398.7</td>\n<td>356</td>\n</tr>\n<tr>\n<td>Highest in 24 hours</td>\n<td>67.2</td>\n<td>53.2</td>\n<td>44.3</td>\n<td>53.8</td>\n</tr>\n<tr>\n<td>Highest in 12 hours</td>\n<td>34.7</td>\n<td>34.1</td>\n<td>29.8</td>\n<td>31.0</td>\n</tr>\n<tr>\n<td colspan="5">2008</td>\n</tr>\n<tr>\n<td>Highest average</td>\n<td>496.7</td>\n<td>444.3</td>\n<td>502.1</td>\n<td>399.6</td>\n</tr>\n<tr>\n<td>Highest in 24 hours</td>\n<td>44.2</td>\n<td>56.7</td>\n<td>32.1</td>\n<td>63.2</td>\n</tr>\n<tr>\n<td>Highest in 12 hours</td>\n<td>30.1</td>\n<td>32.7</td>\n<td>21.9</td>\n<td>40.2</td>\n</tr>\n</table>\n\n\n**Page 11**\n\n', 'language': 'en'}"""

    result = correct_tables(example_html)
    if result:
        print("Final result:")
        print(result)
    else:
        print("Processing failed")
