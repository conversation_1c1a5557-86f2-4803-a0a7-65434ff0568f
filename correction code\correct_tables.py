import os
import json

def expand_spans(table_with_span):
    expanded = []
    occupied_positions = set()

    for r, row in enumerate(table_with_span):
        while len(expanded) <= r:
            expanded.append([])

        c = 0
        for cell in row:
            while (r, c) in occupied_positions:
                c += 1

            rowspan = cell.get("rowspan", 1)
            colspan = cell.get("colspan", 1)

            while len(expanded[r]) < c + colspan:
                expanded[r].append(None)

            expanded[r][c] = cell["text"]

            for rr in range(r, r + rowspan):
                while len(expanded) <= rr:
                    expanded.append([])

                for cc in range(c, c + colspan):
                    while len(expanded[rr]) <= cc:
                        expanded[rr].append(None)

                    if rr == r and cc == c:
                        continue
                    expanded[rr][cc] = None
                    occupied_positions.add((rr, cc))

            c += colspan

    max_cols = max(len(row) for row in expanded)
    for row in expanded:
        while len(row) < max_cols:
            row.append(None)

    return expanded

def correct_table_json(json_path):
    with open(json_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    with_span = data.get("with_span", [])
    expanded = expand_spans(with_span)

    # Save only the expanded table as JSON root content
    with open(json_path, "w", encoding="utf-8") as f:
        json.dump(expanded, f, indent=2, ensure_ascii=False)

def main():
    input_dir = "output_test2"  # directory containing your JSON files
    files = [f for f in os.listdir(input_dir) if f.endswith(".json")]

    for filename in files:
        filepath = os.path.join(input_dir, filename)
        print(f"Correcting {filename}...")
        correct_table_json(filepath)

    print(f"✅ Corrected {len(files)} tables.")

if __name__ == "__main__":
    main()
