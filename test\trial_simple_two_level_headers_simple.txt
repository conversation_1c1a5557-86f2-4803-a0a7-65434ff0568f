{'content': '<table><caption>Table 1: Basic Colspan Header Issue</caption><tr><th colspan="2">Sales Data</th></tr><tr><th>Product</th><th>Revenue</th></tr><tr><td>Product A</td><td>1000</td></tr><tr><td>Product B</td><td>1500</td></tr></table><table><caption>Table 2: Three Column Colspan Issue</caption><tr><th colspan="3">Employee Information</th></tr><tr><th>Name</th><th>Department</th><th>Salary</th></tr><tr><td>John</td><td>IT</td><td>50000</td></tr><tr><td>Jane</td><td>HR</td><td>45000</td></tr></table><table><caption>Table 3: Mixed Colspan Structure</caption><tr><th colspan="2">Q1 Results</th><th>Q2</th></tr><tr><th>Region</th><th>Sales</th><th>Growth</th></tr><tr><td>North</td><td>2000</td><td>5%</td></tr><tr><td>South</td><td>1800</td><td>3%</td></tr></table><table><caption>Table 4: Multiple Colspan Headers</caption><tr><th colspan="2">Financial Data</th><th colspan="2">Performance</th></tr><tr><th>Income</th><th>Expenses</th><th>Profit</th><th>Margin</th></tr><tr><td>10000</td><td>7000</td><td>3000</td><td>30%</td></tr><tr><td>12000</td><td>8000</td><td>4000</td><td>33%</td></tr></table><table><caption>Table 5: Nested Colspan Structure</caption><tr><th colspan="4">Company Overview</th></tr><tr><th colspan="2">Financials</th><th colspan="2">Operations</th></tr><tr><th>Revenue</th><th>Profit</th><th>Employees</th><th>Locations</th></tr><tr><td>50000</td><td>15000</td><td>100</td><td>5</td></tr></table><table><caption>Table 6: Asymmetric Colspan</caption><tr><th colspan="3">Product Analysis</th></tr><tr><th>Product</th><th>Price</th><th>Stock</th></tr><tr><td>Widget A</td><td>25</td><td>100</td></tr><tr><td>Widget B</td><td>30</td><td>75</td></tr><tr><td>Widget C</td><td>35</td><td>50</td></tr></table><table><caption>Table 7: Complex Header Hierarchy</caption><tr><th rowspan="2">Item</th><th colspan="2">2023</th><th colspan="2">2024</th></tr><tr><th>Q1</th><th>Q2</th><th>Q1</th><th>Q2</th></tr><tr><td>Sales</td><td>1000</td><td>1200</td><td>1100</td><td>1300</td></tr><tr><td>Costs</td><td>800</td><td>900</td><td>850</td><td>950</td></tr></table><table><caption>Table 8: Simple Two-Level Headers</caption><tr><th colspan="2">Customer Data</th></tr><tr><th>Name</th><th>Email</th></tr><tr><td>Alice</td><td><EMAIL></td></tr><tr><td>Bob</td><td><EMAIL></td></tr></table><table><caption>Table 9: Uneven Column Distribution</caption><tr><th colspan="2">Team A</th><th>Team B</th></tr><tr><th>Player</th><th>Score</th><th>Score</th></tr><tr><td>John</td><td>95</td><td>88</td></tr><tr><td>Jane</td><td>92</td><td>91</td></tr></table><table><caption>Table 10: Full-Width Header with Subheaders</caption><tr><th colspan="4">Quarterly Report</th></tr><tr><th>Department</th><th>Budget</th><th>Spent</th><th>Remaining</th></tr><tr><td>Marketing</td><td>10000</td><td>8500</td><td>1500</td></tr><tr><td>Development</td><td>15000</td><td>12000</td><td>3000</td></tr></table>', 'language': 'en'}
