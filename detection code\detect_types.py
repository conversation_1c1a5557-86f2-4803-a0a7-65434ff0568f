import os
import json

def analyze_structure(table):
    """Analyze table structure based only on spans and headers"""
    if not isinstance(table, dict) or "with_span" not in table:
        return None
    
    rows = table.get("with_span", [])
    if not rows:
        return None
    
    # Basic counts
    header_cells = 0
    rowspan_cells = 0
    colspan_cells = 0
    max_rowspan = 1
    max_colspan = 1
    
    for row in rows:
        for cell in row:
            if cell.get("tag", "").lower() == "th":
                header_cells += 1
            
            rowspan = cell.get("rowspan", 1)
            colspan = cell.get("colspan", 1)
            
            if rowspan > 1:
                rowspan_cells += 1
                max_rowspan = max(max_rowspan, rowspan)
            if colspan > 1:
                colspan_cells += 1
                max_colspan = max(max_colspan, colspan)
    
    return {
        "header_cells": header_cells,
        "rowspan_cells": rowspan_cells,
        "colspan_cells": colspan_cells,
        "max_rowspan": max_rowspan,
        "max_colspan": max_colspan,
        "is_key_value": all(len(row) == 2 for row in rows) and header_cells == 0
    }

def determine_transform(structure):
    """Determine transformation based purely on span structure"""
    if not structure:
        return "no_correction_needed"
    
    # Key-value pairs (2 columns, no headers)
    if structure["is_key_value"]:
        return "transform_keyed_subitems"
    
    # Tables dominated by rowspans
    if structure["rowspan_cells"] > structure["colspan_cells"]:
        if structure["max_rowspan"] >= 3:
            return "transform_generic_grouped_by_parent"
        return "transform_year_grouped_rows"
    
    # Tables dominated by colspans
    if structure["colspan_cells"] > structure["rowspan_cells"]:
        if structure["max_colspan"] >= 3:
            return "transform_year_columns_nested"
        return "transform_col_indexed_json"
    
    # Balanced span tables
    if structure["header_cells"] > 0:
        return "transform_json_table"
    
    return "no_correction_needed"

def process_files(input_dir):
    """Process all JSON files in directory"""
    for filename in sorted(os.listdir(input_dir)):
        if not filename.endswith(".json"):
            continue
        
        try:
            with open(os.path.join(input_dir, filename), "r", encoding="utf-8") as f:
                data = json.load(f)
            
            structure = analyze_structure(data)
            transform = determine_transform(structure)
            print(f"{filename}: {transform}")
            
        except Exception:
            print(f"{filename}: no_correction_needed")

if __name__ == "__main__":
    INPUT_DIR = "output"  # Change to your directory
    process_files(INPUT_DIR)
"""
"""




# import os
# import json
# import re
# from collections import defaultdict

# INPUT_DIR = "output"  # Default folder

# def is_year(s):
#     try:
#         return 1900 <= int(s.strip()) <= 2100
#     except:
#         return False

# def analyze_with_span(table):
#     features = {
#         "valid": True,
#         "row_count": len(table),
#         "col_counts": [len(row) for row in table],
#         "has_duplicate_headers": False,
#         "has_empty_headers": False,
#         "has_rowspan_in_first_col": False,
#         "has_colspan_in_header": False,
#         "first_col_years": False,
#         "has_year_in_headers": False,
#         "is_two_col_table": False,
#     }

#     if not table or not isinstance(table, list) or not all(isinstance(row, list) for row in table):
#         features["valid"] = False
#         return features

#     # Header row
#     header_row = table[0]
#     header_texts = [cell.get("text", "").strip() for cell in header_row if isinstance(cell, dict)]
#     seen = set()
#     for h in header_texts:
#         if not h:
#             features["has_empty_headers"] = True
#         if h in seen:
#             features["has_duplicate_headers"] = True
#         seen.add(h)
#         if re.match(r"\d{4}", h):
#             features["has_year_in_headers"] = True
#     if any(cell.get("colspan", 1) > 1 for cell in header_row):
#         features["has_colspan_in_header"] = True

#     # Check for rowspan in first column
#     for row in table[1:]:
#         if row and isinstance(row[0], dict) and row[0].get("rowspan", 1) > 1:
#             features["has_rowspan_in_first_col"] = True
#             break

#     # Check first column for years
#     first_col = [row[0]["text"] for row in table[1:] if row and isinstance(row[0], dict)]
#     year_like = [val for val in first_col if is_year(val)]
#     if len(year_like) >= max(2, len(first_col) // 2):
#         features["first_col_years"] = True

#     # Check 2-column layout
#     if all(len(row) == 2 for row in table):
#         features["is_two_col_table"] = True

#     return features

# def classify_table(features):
#     if not features["valid"]:
#         return "not_a_valid_table"

#     if features["first_col_years"]:
#         return "transform_year_grouped_rows"

#     if features["has_rowspan_in_first_col"] and not features["has_colspan_in_header"]:
#         return "transform_generic_grouped_by_parent"

#     if features["has_year_in_headers"] and features["has_colspan_in_header"]:
#         return "transform_year_columns_nested"

#     if features["is_two_col_table"]:
#         return "transform_keyed_subitems"

#     if features["has_duplicate_headers"] or features["has_empty_headers"]:
#         return "transform_json_table"

#     return "transform_col_indexed_json"

# def detect_folder_with_span(folder_path):
#     results = []
#     for fname in sorted(os.listdir(folder_path)):
#         if not fname.endswith(".json"):
#             continue
#         try:
#             with open(os.path.join(folder_path, fname), "r", encoding="utf-8") as f:
#                 data = json.load(f)

#             with_span = data.get("with_span")
#             if not with_span:
#                 results.append((fname, "not_a_valid_table"))
#                 continue

#             features = analyze_with_span(with_span)
#             group = classify_table(features)
#             results.append((fname, group))

#         except Exception as e:
#             results.append((fname, f"error: {str(e)}"))

#     return results

# def print_group_summary(results):
#     groups = defaultdict(list)
#     for fname, group in results:
#         groups[group].append(fname)

#     print("\n📦 Summary by group:")
#     for group, files in sorted(groups.items()):
#         print(f"  {group} ({len(files)} files):")
#         for f in files:
#             print(f"    - {f}")

# if __name__ == "__main__":
#     folder = INPUT_DIR
#     results = detect_folder_with_span(folder)
#     print_group_summary(results)
