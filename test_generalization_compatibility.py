#!/usr/bin/env python3
"""
Test script to verify that the generalized code_version_2.py produces 
identical outputs to the original code_version_1.py for all test files.
"""

import os
import sys
import json
import difflib
from pathlib import Path

# Import both versions
import code_version_1 as original
import code_version_2 as generalized

def load_test_file(filepath):
    """Load test file with proper encoding detection."""
    try:
        # Try UTF-8 first
        with open(filepath, 'r', encoding='utf-8') as f:
            return f.read()
    except UnicodeDecodeError:
        try:
            # Try UTF-16 if UTF-8 fails
            with open(filepath, 'r', encoding='utf-16') as f:
                return f.read()
        except UnicodeDecodeError:
            # Fallback to latin-1
            with open(filepath, 'r', encoding='latin-1') as f:
                return f.read()

def normalize_json_output(output_str):
    """Normalize JSON output for comparison by parsing and re-serializing."""
    try:
        # Extract JSON parts from the output
        import re
        json_pattern = r'(\[.*?\]|\{.*?\})'
        matches = re.findall(json_pattern, output_str, re.DOTALL)
        
        normalized_parts = []
        for match in matches:
            try:
                # Parse and re-serialize to normalize formatting
                parsed = json.loads(match)
                normalized = json.dumps(parsed, sort_keys=True, separators=(',', ':'))
                normalized_parts.append(normalized)
            except json.JSONDecodeError:
                # If not valid JSON, keep as is
                normalized_parts.append(match)
        
        # Replace original JSON parts with normalized ones
        result = output_str
        for i, match in enumerate(matches):
            if i < len(normalized_parts):
                result = result.replace(match, normalized_parts[i], 1)
        
        return result
    except Exception:
        return output_str

def compare_outputs(original_output, generalized_output, test_file):
    """Compare two outputs and return detailed comparison results."""
    # Normalize both outputs
    norm_original = normalize_json_output(original_output)
    norm_generalized = normalize_json_output(generalized_output)
    
    if norm_original == norm_generalized:
        return True, "✅ IDENTICAL"
    
    # If not identical, provide detailed diff
    diff = list(difflib.unified_diff(
        norm_original.splitlines(keepends=True),
        norm_generalized.splitlines(keepends=True),
        fromfile=f"original_{test_file}",
        tofile=f"generalized_{test_file}",
        lineterm=""
    ))
    
    return False, "❌ DIFFERENT:\n" + "".join(diff)

def test_single_file(test_file_path):
    """Test a single file with both versions."""
    print(f"\n🔍 Testing: {test_file_path}")
    
    try:
        # Load test content
        content = load_test_file(test_file_path)
        
        # Test with original version
        try:
            original_output = original.correct_tables(content)
        except Exception as e:
            print(f"❌ Original version failed: {e}")
            return False
        
        # Test with generalized version
        try:
            generalized_output = generalized.correct_tables(content)
        except Exception as e:
            print(f"❌ Generalized version failed: {e}")
            return False
        
        # Compare outputs
        is_identical, comparison_result = compare_outputs(
            original_output, generalized_output, os.path.basename(test_file_path)
        )
        
        print(f"   {comparison_result}")
        
        if not is_identical:
            # Save outputs for manual inspection
            base_name = os.path.splitext(os.path.basename(test_file_path))[0]
            with open(f"debug_original_{base_name}.txt", 'w', encoding='utf-8') as f:
                f.write(original_output)
            with open(f"debug_generalized_{base_name}.txt", 'w', encoding='utf-8') as f:
                f.write(generalized_output)
            print(f"   📁 Debug files saved: debug_original_{base_name}.txt, debug_generalized_{base_name}.txt")
        
        return is_identical
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive test on all test files."""
    print("🚀 Starting Comprehensive Generalization Compatibility Test")
    print("=" * 60)
    
    # Find all test files
    test_dir = Path("test")
    if not test_dir.exists():
        print("❌ Test directory not found!")
        return False
    
    test_files = list(test_dir.glob("*.txt"))
    if not test_files:
        print("❌ No test files found!")
        return False
    
    print(f"📂 Found {len(test_files)} test files")
    
    # Run tests
    passed = 0
    failed = 0
    failed_files = []
    
    for test_file in sorted(test_files):
        if test_single_file(test_file):
            passed += 1
        else:
            failed += 1
            failed_files.append(test_file.name)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed_files:
        print(f"\n❌ Failed Files:")
        for file in failed_files:
            print(f"   - {file}")
    
    return failed == 0

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
