#!/usr/bin/env python3
"""
Comprehensive table analyzer to identify all possible groupings based on structural patterns.
"""

import os
import json
import re
from typing import Dict, List, Any, Tuple
from collections import defaultdict, Counter

def load_table_data(filepath: str) -> Dict[str, Any]:
    """Load table data from JSON file."""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return {}

def extract_comprehensive_features(table_data: Dict[str, Any], table_num: int) -> Dict[str, Any]:
    """Extract comprehensive structural features from table data."""
    if not table_data or 'with_span' not in table_data:
        return {}
    
    rows = table_data['with_span']
    if not rows:
        return {}
    
    features = {
        'table_num': table_num,
        'total_rows': len(rows),
        'total_cells': sum(len(row) for row in rows),
        'header_cells': 0,
        'data_cells': 0,
        'rowspan_cells': 0,
        'colspan_cells': 0,
        'max_rowspan': 1,
        'max_colspan': 1,
        'empty_cells': 0,
        'cells_per_row': [],
        'first_row_structure': [],
        'first_col_structure': [],
        'span_positions': [],
        'header_row_count': 0,
        'empty_ratio': 0.0,
        'header_ratio': 0.0,
        'span_complexity': 'none',
        'column_consistency': True,
        'has_year_text': False,
        'has_numeric_data': False,
        'table_shape': '',
        'dominant_cell_type': '',
        'first_row_colspan_pattern': [],
        'first_col_rowspan_pattern': [],
        'large_spans_count': 0,
        'section_indicators': 0
    }
    
    # Analyze each row
    for row_idx, row in enumerate(rows):
        row_cell_count = len(row)
        features['cells_per_row'].append(row_cell_count)
        
        # Check if this is a header row
        if all(cell.get('tag', '').lower() == 'th' for cell in row):
            features['header_row_count'] += 1
        
        # Analyze each cell
        for col_idx, cell in enumerate(row):
            tag = cell.get('tag', '').lower()
            text = cell.get('text', '').strip()
            rowspan = cell.get('rowspan', 1)
            colspan = cell.get('colspan', 1)
            
            # Count cell types
            if tag == 'th':
                features['header_cells'] += 1
            else:
                features['data_cells'] += 1
            
            # Count empty cells
            if not text:
                features['empty_cells'] += 1
            
            # Check for year-like text
            if re.search(r'\b\d{4}\b', text):
                features['has_year_text'] = True
            
            # Check for numeric data
            if re.search(r'\d+\.?\d*', text):
                features['has_numeric_data'] = True
            
            # Analyze spans
            if rowspan > 1:
                features['rowspan_cells'] += 1
                features['max_rowspan'] = max(features['max_rowspan'], rowspan)
                features['span_positions'].append(('rowspan', row_idx, col_idx, rowspan))
                
                if rowspan >= 3:
                    features['large_spans_count'] += 1
                    if col_idx == 0:
                        features['section_indicators'] += 1
            
            if colspan > 1:
                features['colspan_cells'] += 1
                features['max_colspan'] = max(features['max_colspan'], colspan)
                features['span_positions'].append(('colspan', row_idx, col_idx, colspan))
                
                if colspan >= 3:
                    features['large_spans_count'] += 1
            
            # Store first row and column structures
            if row_idx == 0:
                features['first_row_structure'].append({
                    'tag': tag, 'text': text, 'rowspan': rowspan, 'colspan': colspan
                })
                features['first_row_colspan_pattern'].append(colspan)
            
            if col_idx == 0:
                features['first_col_structure'].append({
                    'tag': tag, 'text': text, 'rowspan': rowspan, 'colspan': colspan
                })
                features['first_col_rowspan_pattern'].append(rowspan)
    
    # Calculate ratios and derived features
    if features['total_cells'] > 0:
        features['empty_ratio'] = features['empty_cells'] / features['total_cells']
        features['header_ratio'] = features['header_cells'] / features['total_cells']
    
    # Determine span complexity
    if features['rowspan_cells'] > features['colspan_cells']:
        features['span_complexity'] = 'rowspan_dominant'
    elif features['colspan_cells'] > features['rowspan_cells']:
        features['span_complexity'] = 'colspan_dominant'
    elif features['rowspan_cells'] > 0 or features['colspan_cells'] > 0:
        features['span_complexity'] = 'mixed_spans'
    else:
        features['span_complexity'] = 'no_spans'
    
    # Determine column consistency
    if len(set(features['cells_per_row'])) > 1:
        features['column_consistency'] = False
    
    # Determine table shape
    if features['total_rows'] <= 5:
        features['table_shape'] = 'small'
    elif features['total_rows'] <= 10:
        features['table_shape'] = 'medium'
    else:
        features['table_shape'] = 'large'
    
    # Determine dominant cell type
    if features['header_cells'] > features['data_cells']:
        features['dominant_cell_type'] = 'header_heavy'
    elif features['data_cells'] > features['header_cells'] * 2:
        features['dominant_cell_type'] = 'data_heavy'
    else:
        features['dominant_cell_type'] = 'balanced'
    
    return features

def identify_groupings(all_features: List[Dict[str, Any]]) -> Dict[str, List[int]]:
    """Identify possible groupings based on various criteria."""
    groupings = {}
    
    # Group 1: By span complexity
    span_groups = defaultdict(list)
    for f in all_features:
        span_groups[f['span_complexity']].append(f['table_num'])
    
    for complexity, tables in span_groups.items():
        if len(tables) > 1:
            groupings[f"span_{complexity}"] = sorted(tables)
    
    # Group 2: By table shape and size
    shape_groups = defaultdict(list)
    for f in all_features:
        key = f"{f['table_shape']}_{f['total_rows']}rows"
        shape_groups[key].append(f['table_num'])
    
    for shape, tables in shape_groups.items():
        if len(tables) > 1:
            groupings[f"shape_{shape}"] = sorted(tables)
    
    # Group 3: By first row colspan pattern
    colspan_pattern_groups = defaultdict(list)
    for f in all_features:
        pattern = tuple(f['first_row_colspan_pattern'])
        if len(pattern) > 0:
            colspan_pattern_groups[pattern].append(f['table_num'])
    
    for pattern, tables in colspan_pattern_groups.items():
        if len(tables) > 1:
            pattern_str = "_".join(map(str, pattern))
            groupings[f"first_row_colspan_{pattern_str}"] = sorted(tables)
    
    # Group 4: By empty ratio ranges
    empty_ratio_groups = defaultdict(list)
    for f in all_features:
        if f['empty_ratio'] < 0.1:
            empty_ratio_groups['low_empty'].append(f['table_num'])
        elif f['empty_ratio'] < 0.3:
            empty_ratio_groups['medium_empty'].append(f['table_num'])
        else:
            empty_ratio_groups['high_empty'].append(f['table_num'])
    
    for ratio_type, tables in empty_ratio_groups.items():
        if len(tables) > 1:
            groupings[f"empty_ratio_{ratio_type}"] = sorted(tables)
    
    # Group 5: By header structure
    header_groups = defaultdict(list)
    for f in all_features:
        if f['header_row_count'] == 0:
            header_groups['no_headers'].append(f['table_num'])
        elif f['header_row_count'] == 1:
            header_groups['single_header'].append(f['table_num'])
        else:
            header_groups['multi_headers'].append(f['table_num'])
    
    for header_type, tables in header_groups.items():
        if len(tables) > 1:
            groupings[f"headers_{header_type}"] = sorted(tables)
    
    # Group 6: By specific structural patterns
    pattern_groups = defaultdict(list)
    for f in all_features:
        # Function/subfunction pattern
        if (f['total_rows'] == 8 and len(f['first_row_colspan_pattern']) == 3 and 
            f['first_row_colspan_pattern'][0] == 2 and f['section_indicators'] == 2):
            pattern_groups['function_subfunction'].append(f['table_num'])
        
        # Multi-header year pattern
        elif (4 <= f['total_rows'] <= 7 and len(f['first_row_colspan_pattern']) == 3 and
              f['first_row_colspan_pattern'][1:] == [2, 2] and f['rowspan_cells'] == 0):
            pattern_groups['multi_header_years'].append(f['table_num'])
        
        # Large colspan pattern
        elif f['max_colspan'] >= 4:
            pattern_groups['large_colspans'].append(f['table_num'])
        
        # High empty with minimal spans
        elif f['empty_ratio'] > 0.15 and f['rowspan_cells'] == 0 and f['colspan_cells'] <= 1:
            pattern_groups['financial_sections'].append(f['table_num'])
        
        # Complex financial
        elif f['empty_ratio'] > 0.4 and f['colspan_cells'] >= 2:
            pattern_groups['complex_financial'].append(f['table_num'])
        
        # Rowspan dominant
        elif f['rowspan_cells'] > f['colspan_cells'] and f['rowspan_cells'] >= 3:
            pattern_groups['rowspan_heavy'].append(f['table_num'])
    
    for pattern, tables in pattern_groups.items():
        if len(tables) > 1:
            groupings[f"pattern_{pattern}"] = sorted(tables)
    
    return groupings

def analyze_all_tables(folder_path: str):
    """Analyze all tables and identify groupings."""
    if not os.path.exists(folder_path):
        print(f"Folder {folder_path} does not exist")
        return

    all_features = []

    # Get all JSON files
    json_files = [f for f in os.listdir(folder_path) if f.endswith('.json')]
    json_files.sort(key=lambda x: int(re.search(r'(\d+)', x).group(1)) if re.search(r'(\d+)', x) else 0)

    print(f"Analyzing {len(json_files)} tables...")

    for filename in json_files:
        filepath = os.path.join(folder_path, filename)
        table_num = int(re.search(r'(\d+)', filename).group(1)) if re.search(r'(\d+)', filename) else 0

        table_data = load_table_data(filepath)
        if table_data:
            features = extract_comprehensive_features(table_data, table_num)
            if features:
                all_features.append(features)

    # Identify groupings
    groupings = identify_groupings(all_features)

    # Print results
    print("=" * 80)
    print("COMPREHENSIVE TABLE GROUPING ANALYSIS")
    print("=" * 80)

    print(f"\n📊 SUMMARY:")
    print(f"   Total tables analyzed: {len(all_features)}")
    print(f"   Total groupings found: {len(groupings)}")

    print(f"\n🔍 ALL POSSIBLE GROUPINGS:")

    for group_name, tables in sorted(groupings.items()):
        print(f"\n✅ {group_name}: {tables}")

        # Show basis for grouping
        if group_name.startswith('span_'):
            print(f"   Basis: Tables with {group_name.split('_')[1]} span structure")
        elif group_name.startswith('shape_'):
            print(f"   Basis: Tables with {group_name.split('_')[1]} size/shape")
        elif group_name.startswith('first_row_colspan_'):
            pattern = group_name.split('_')[3:]
            print(f"   Basis: Tables with first row colspan pattern {pattern}")
        elif group_name.startswith('empty_ratio_'):
            ratio_type = group_name.split('_')[2]
            print(f"   Basis: Tables with {ratio_type} empty cell ratio")
        elif group_name.startswith('headers_'):
            header_type = group_name.split('_')[1]
            print(f"   Basis: Tables with {header_type} structure")
        elif group_name.startswith('pattern_'):
            pattern_type = group_name.split('_')[1]
            print(f"   Basis: Tables matching {pattern_type} structural pattern")

    # Show detailed features for interesting groups
    print(f"\n📋 DETAILED ANALYSIS OF KEY GROUPS:")

    key_groups = [g for g in groupings.keys() if g.startswith('pattern_')]
    for group_name in key_groups:
        tables = groupings[group_name]
        print(f"\n🔍 {group_name}: {tables}")

        for table_num in tables:
            features = next(f for f in all_features if f['table_num'] == table_num)
            print(f"   Table {table_num}:")
            print(f"      Size: {features['total_rows']}x{max(features['cells_per_row']) if features['cells_per_row'] else 0}")
            print(f"      Spans: {features['rowspan_cells']} rowspans (max: {features['max_rowspan']}), {features['colspan_cells']} colspans (max: {features['max_colspan']})")
            print(f"      Empty ratio: {features['empty_ratio']:.2f}")
            print(f"      First row pattern: {features['first_row_colspan_pattern']}")
            print(f"      Complexity: {features['span_complexity']}")

def main():
    """Main function."""
    folder_path = "output"
    analyze_all_tables(folder_path)

if __name__ == "__main__":
    main()
