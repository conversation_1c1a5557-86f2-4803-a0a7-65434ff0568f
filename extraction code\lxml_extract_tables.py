import os
import json
from lxml import html

def extract_table_with_span(table_el):
    rows = table_el.xpath(".//tr")
    table_data = []

    for row in rows:
        row_data = []
        for cell in row.xpath("./th|./td"):
            row_data.append({
                "tag": cell.tag,
                "text": ''.join(cell.itertext()).strip(),
                "rowspan": int(cell.get("rowspan", "1")),
                "colspan": int(cell.get("colspan", "1"))
            })
        table_data.append(row_data)
    return table_data

def extract_table_no_span(table_el):
    rows = table_el.xpath(".//tr")
    simplified = []

    for row in rows:
        row_data = []
        for cell in row.xpath("./th|./td"):
            row_data.append(''.join(cell.itertext()).strip())
        simplified.append(row_data)
    return simplified

def process_tables_from_html(html_content):
    tree = html.fromstring(html_content)
    tables = tree.xpath("//table")
    output = []

    for idx, table in enumerate(tables, 1):
        caption_el = table.xpath("./caption")
        caption = caption_el[0].text.strip() if caption_el else None
        output.append({
            "index": idx,
            "caption": caption,
            "with_span": extract_table_with_span(table),
            #"no_span": extract_table_no_span(table)
        })
    return output

def main():
    input_file = "di_parser_output.txt"
    output_dir = "output"
    os.makedirs(output_dir, exist_ok=True)

    with open(input_file, "r", encoding="utf-8") as f:
        html_content = f.read()

    tables = process_tables_from_html(html_content)

    for table in tables:
        filepath = os.path.join(output_dir, f"table_{table['index']}.json")
        with open(filepath, "w", encoding="utf-8") as out_file:
            json.dump(table, out_file, indent=2, ensure_ascii=False)

    print(f"✅ Saved {len(tables)} tables to '{output_dir}/'")

if __name__ == "__main__":
    main()
