{'content': '<table><caption>Sales Report - Q1</caption><title>Region - Product A - Product B</title>[{"Region":"North","Product A":"120","Product B":"98"},{"Region":"South","Product A":"140","Product B":"110"},{"Region":"East","Product A":"130","Product B":"105"},{"Region":"West","Product A":"115","Product B":"95"}]</table><br><table><caption>Table 2: Department Budget Allocation</caption>[{"Department":"Operations,Logistics","2023":"2.3M","2024":"2.6M"},{"Department":"Operations,Manufacturing","2023":"4.5M","2024":"4.8M"},{"Department":"Support,IT","2023":"1.1M","2024":"1.3M"},{"Department":"Support,HR","2023":"900K","2024":"950K"},{"Department":"Total,Total","2023":"8.8M","2024":"9.65M"}]</table><br><table><caption>Table 3: Weekly Schedule</caption>[{"Day":"Monday","Morning":"Team Meeting","Afternoon":"Client Call","Evening":"Project Review"},{"Day":"Tuesday","Morning":"Development","Afternoon":"QA Testing","Evening":"Planning"},{"Day":"Wednesday","Morning":"Workshop","Afternoon":"Break","Evening":"Design Review"},{"Day":"Thursday","Morning":"Code Review","Afternoon":"Scrum","Evening":"1:1 Meetings"},{"Day":"Friday","Morning":"Deployment","Afternoon":"Retrospective","Evening":"Wrap-up"}]</table><br><table><caption>Table 1</caption>{"Task A":{"status":"In Progress","details":"Due: Friday"}}</table><br><table><caption>Table 2</caption>{"Name":"John Smith","Role":"Designer","Start Date":"01-Feb-2024"}</table><br><table><caption>Table 3</caption>{"Apple":"Red","Banana":"Yellow","Grapes":"Purple"}</table><br><table><caption>Table 4</caption>[{"Service":"API","Status":"Running"},{"Service":"Database","Status":"Offline"},{"Service":"Web","Status":"Running","column":"Cached"}]</table><br><table><caption>Table 5</caption>{"Username":"@mira_x","Followers":"12.5k","Posts":"134"}</table><br><table><caption>Table 6</caption>[{"Stage":"Planning","Date":"Mar 1","Status":"Done"},{"Stage":"Design","Date":"Mar 5","Status":"In Progress"},{"Stage":"Launch","Date":"Apr 1","Status":"Upcoming"}]</table><br><table><caption>Table 7</caption>{"column":"Alpha,Beta,Gamma,Delta,Epsilon"}</table><br><table><caption>Table 8</caption>[{"Country":"Capital","Japan":"Tokyo"},{"Country":"Population","Japan":"125M"}]</table><br><table><caption>Table 9</caption>[{"A1":"B1","A2":"B2","A3":"B3"},{"A1":"C1","A2":"C2","A3":"C3"}]</table><br><table><caption>Table 10</caption>[{"Row":"Metric","Header: Report Summary":"Value"},{"Row":"Visits","Header: Report Summary":"5,202"},{"Row":"Conversions","Header: Report Summary":"312"}]</table>'}